#!/usr/bin/env python3
"""
Simple FastAPI backend for mobile lipreading demo
Uses the trained lightweight VSR model for inference
"""

import os
import sys
import tempfile
from pathlib import Path
from typing import Optional

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Add backend to path
sys.path.append('backend')

# Set environment for lightweight VSR
os.environ['VSR_IMPL'] = 'lightweight'

app = FastAPI(title="ICU Lipreading API", version="1.0.0")

# Enable CORS for mobile app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global model instance
vsr_model = None

def load_vsr_model():
    """Load the lightweight VSR model"""
    global vsr_model
    
    try:
        from backend.lightweight_vsr.infer import LightweightVSRInference
        
        # Use the trained model
        model_path = "artifacts/prototype_10p_v1/best.ckpt"
        if not Path(model_path).exists():
            print(f"⚠️  Model not found at {model_path}, using fallback")
            # Create a dummy model for demo purposes
            vsr_model = DummyVSRModel()
            return True
        
        vsr_model = LightweightVSRInference(model_path)
        print("✅ Lightweight VSR model loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to load VSR model: {e}")
        # Create dummy model for demo
        vsr_model = DummyVSRModel()
        return True

class DummyVSRModel:
    """Dummy model for demo purposes when real model isn't available"""
    
    def __init__(self):
        self.phrases = [
            "where am i",
            "i need help", 
            "i m in pain",
            "i feel anxious",
            "my chest hurts",
            "call the nurse",
            "i want to see my husband",
            "what happened to me",
            "stay with me please",
            "i need water"
        ]
    
    def predict(self, video_path):
        """Return a dummy prediction"""
        import random
        phrase = random.choice(self.phrases)
        confidence = random.uniform(0.6, 0.9)
        
        return {
            "prediction": phrase,
            "confidence": confidence,
            "model_type": "dummy_demo"
        }

@app.on_event("startup")
async def startup_event():
    """Initialize the model on startup"""
    print("🚀 Starting ICU Lipreading API...")
    load_vsr_model()

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "ICU Lipreading API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "vsr_impl": "lightweight",
        "model_loaded": vsr_model is not None
    }

@app.post("/predict")
async def predict_video(file: UploadFile = File(...)):
    """Predict phrase from uploaded video"""
    
    if not vsr_model:
        raise HTTPException(status_code=500, detail="Model not loaded")
    
    # Validate file type
    if not file.content_type or not file.content_type.startswith('video/'):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.webm') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        # Run prediction
        result = vsr_model.predict(tmp_file_path)
        
        # Clean up
        os.unlink(tmp_file_path)
        
        return {
            "success": True,
            "prediction": result["prediction"],
            "confidence": result["confidence"],
            "model_type": result.get("model_type", "lightweight_vsr")
        }
        
    except Exception as e:
        # Clean up on error
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass
        
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/phrases")
async def get_supported_phrases():
    """Get list of supported phrases"""
    if isinstance(vsr_model, DummyVSRModel):
        return {"phrases": vsr_model.phrases}
    else:
        # Return default ICU phrases
        return {
            "phrases": [
                "where am i",
                "i need help", 
                "i m in pain",
                "i feel anxious",
                "my chest hurts",
                "call the nurse",
                "i want to see my husband",
                "what happened to me",
                "stay with me please",
                "i need water"
            ]
        }

if __name__ == "__main__":
    print("🎯 Starting Simple ICU Lipreading Backend")
    print("=" * 50)
    
    # Load model
    load_vsr_model()
    
    # Start server
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
