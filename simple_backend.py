#!/usr/bin/env python3
"""
Simple FastAPI backend for mobile lipreading demo
Uses the trained lightweight VSR model for inference
"""

import os
import sys
import tempfile
import time
import logging
from pathlib import Path
from typing import Optional
from datetime import datetime

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Add backend to path
sys.path.append('backend')

# Set environment for lightweight VSR
os.environ['VSR_IMPL'] = 'lightweight'

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="ICU Lipreading API", version="1.0.0")

# Performance tracking
performance_stats = {
    "total_requests": 0,
    "successful_predictions": 0,
    "failed_predictions": 0,
    "average_processing_time": 0.0,
    "last_request_time": None,
    "uptime_start": datetime.now()
}

# Enable CORS for mobile app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global model instance
vsr_model = None

def load_vsr_model():
    """Load the lightweight VSR model"""
    global vsr_model
    
    try:
        from backend.lightweight_vsr.infer import LightweightVSRInference
        
        # Use the trained model
        model_path = "artifacts/prototype_10p_v1/best.ckpt"
        if not Path(model_path).exists():
            print(f"⚠️  Model not found at {model_path}, using fallback")
            # Create a dummy model for demo purposes
            vsr_model = DummyVSRModel()
            return True
        
        vsr_model = LightweightVSRInference(model_path)
        print("✅ Lightweight VSR model loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to load VSR model: {e}")
        # Create dummy model for demo
        vsr_model = DummyVSRModel()
        return True

class DummyVSRModel:
    """Dummy model for demo purposes when real model isn't available"""
    
    def __init__(self):
        self.phrases = [
            "where am i",
            "i need help", 
            "i m in pain",
            "i feel anxious",
            "my chest hurts",
            "call the nurse",
            "i want to see my husband",
            "what happened to me",
            "stay with me please",
            "i need water"
        ]
    
    def predict(self, video_path):
        """Return a dummy prediction"""
        import random
        phrase = random.choice(self.phrases)
        confidence = random.uniform(0.6, 0.9)
        
        return {
            "prediction": phrase,
            "confidence": confidence,
            "model_type": "dummy_demo"
        }

@app.on_event("startup")
async def startup_event():
    """Initialize the model on startup"""
    print("🚀 Starting ICU Lipreading API...")
    load_vsr_model()

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "ICU Lipreading API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "vsr_impl": "lightweight",
        "model_loaded": vsr_model is not None
    }

@app.post("/predict")
async def predict_video(file: UploadFile = File(...)):
    """Predict phrase from uploaded video with performance tracking"""
    global performance_stats

    start_time = time.time()
    performance_stats["total_requests"] += 1
    performance_stats["last_request_time"] = datetime.now()

    logger.info(f"🎥 Received prediction request - File: {file.filename}, Size: {file.size if hasattr(file, 'size') else 'unknown'}")

    if not vsr_model:
        performance_stats["failed_predictions"] += 1
        raise HTTPException(status_code=500, detail="Model not loaded")

    # Validate file type (be more lenient for mobile uploads)
    if file.content_type and not (file.content_type.startswith('video/') or
                                  file.content_type.startswith('application/octet-stream')):
        performance_stats["failed_predictions"] += 1
        logger.warning(f"❌ Invalid content type: {file.content_type}")
        raise HTTPException(status_code=400, detail="File must be a video")

    # Also check file extension as fallback
    if file.filename:
        valid_extensions = ['.mp4', '.webm', '.mov', '.avi']
        if not any(file.filename.lower().endswith(ext) for ext in valid_extensions):
            performance_stats["failed_predictions"] += 1
            logger.warning(f"❌ Invalid file extension: {file.filename}")
            raise HTTPException(status_code=400, detail="File must be a video (mp4, webm, mov, or avi)")

    try:
        # Save uploaded file temporarily
        upload_start = time.time()
        with tempfile.NamedTemporaryFile(delete=False, suffix='.webm') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name

        upload_time = time.time() - upload_start
        file_size = len(content)
        logger.info(f"📁 File uploaded - Size: {file_size} bytes, Upload time: {upload_time:.2f}s")

        # Run prediction
        prediction_start = time.time()
        result = vsr_model.predict(tmp_file_path)
        prediction_time = time.time() - prediction_start

        # Clean up
        os.unlink(tmp_file_path)

        # Update performance stats
        total_time = time.time() - start_time
        performance_stats["successful_predictions"] += 1
        performance_stats["average_processing_time"] = (
            (performance_stats["average_processing_time"] * (performance_stats["successful_predictions"] - 1) + total_time) /
            performance_stats["successful_predictions"]
        )

        logger.info(f"✅ Prediction successful - Phrase: '{result['prediction']}', Confidence: {result['confidence']:.3f}, Total time: {total_time:.2f}s (Upload: {upload_time:.2f}s, Prediction: {prediction_time:.2f}s)")

        return {
            "success": True,
            "prediction": result["prediction"],
            "confidence": result["confidence"],
            "model_type": result.get("model_type", "lightweight_vsr"),
            "processing_time": total_time,
            "file_size": file_size
        }

    except Exception as e:
        # Clean up on error
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass

        performance_stats["failed_predictions"] += 1
        error_time = time.time() - start_time
        logger.error(f"❌ Prediction failed after {error_time:.2f}s - Error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/phrases")
async def get_supported_phrases():
    """Get list of supported phrases"""
    if isinstance(vsr_model, DummyVSRModel):
        return {"phrases": vsr_model.phrases}
    else:
        # Return default ICU phrases
        return {
            "phrases": [
                "where am i",
                "i need help",
                "i m in pain",
                "i feel anxious",
                "my chest hurts",
                "call the nurse",
                "i want to see my husband",
                "what happened to me",
                "stay with me please",
                "i need water"
            ]
        }

@app.get("/stats")
async def get_performance_stats():
    """Get performance statistics for monitoring"""
    uptime = datetime.now() - performance_stats["uptime_start"]

    return {
        "performance": performance_stats,
        "uptime_seconds": uptime.total_seconds(),
        "uptime_formatted": str(uptime),
        "requests_per_minute": performance_stats["total_requests"] / max(uptime.total_seconds() / 60, 1),
        "success_rate": (
            performance_stats["successful_predictions"] / max(performance_stats["total_requests"], 1) * 100
        ),
        "model_status": {
            "loaded": vsr_model is not None,
            "type": "dummy_demo" if isinstance(vsr_model, DummyVSRModel) else "lightweight_vsr"
        }
    }

@app.get("/test-connection")
async def test_connection():
    """Simple endpoint for connection testing"""
    return {
        "status": "connected",
        "timestamp": datetime.now().isoformat(),
        "server": "ICU Lipreading API",
        "ready": vsr_model is not None
    }

if __name__ == "__main__":
    print("🎯 Starting Simple ICU Lipreading Backend")
    print("=" * 50)
    
    # Load model
    load_vsr_model()
    
    # Start server
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
