#!/usr/bin/env python3
"""
Debug the tensor shapes in the preprocessed dataset
"""

import torch
import json
from pathlib import Path

def debug_tensor_shapes():
    """Check the shapes of preprocessed tensors"""
    print("🔍 Debugging Tensor Shapes")
    print("=" * 40)
    
    manifest_path = "preprocessed_mouth_detected/processed_manifest.json"
    if not Path(manifest_path).exists():
        print("❌ Manifest not found")
        return
    
    with open(manifest_path) as f:
        manifest = json.load(f)
    
    print(f"📊 Checking {min(5, len(manifest))} sample tensors...")
    
    for i in range(min(5, len(manifest))):
        item = manifest[i]
        tensor_path = item['preprocessed_path']
        
        if Path(tensor_path).exists():
            tensor = torch.load(tensor_path)
            print(f"  {i+1}. {Path(tensor_path).name}")
            print(f"     Shape: {tensor.shape}")
            print(f"     Dtype: {tensor.dtype}")
            print(f"     Min/Max: {tensor.min():.3f}/{tensor.max():.3f}")
            print()
        else:
            print(f"  {i+1}. {Path(tensor_path).name} - NOT FOUND")

if __name__ == "__main__":
    debug_tensor_shapes()
