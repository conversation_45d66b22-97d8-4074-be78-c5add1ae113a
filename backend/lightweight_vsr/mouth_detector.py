"""
Mouth Detection and Cropping Module for VSR Preprocessing
Uses MediaPipe for robust face landmark detection and mouth region extraction
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)

class MouthDetector:
    """
    Detects and crops mouth regions from video frames using MediaPipe face landmarks
    """
    
    def __init__(self, 
                 mouth_crop_size: Tuple[int, int] = (96, 96),
                 mouth_margin: float = 0.3,
                 min_detection_confidence: float = 0.5,
                 min_tracking_confidence: float = 0.5):
        """
        Initialize mouth detector
        
        Args:
            mouth_crop_size: Target size for cropped mouth region (width, height)
            mouth_margin: Extra margin around mouth landmarks (0.3 = 30% extra)
            min_detection_confidence: Minimum confidence for face detection
            min_tracking_confidence: Minimum confidence for face tracking
        """
        self.mouth_crop_size = mouth_crop_size
        self.mouth_margin = mouth_margin
        
        # Initialize MediaPipe Face Mesh
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        
        # Mouth landmark indices (MediaPipe face mesh)
        # Outer lip landmarks for robust mouth region detection
        self.mouth_landmarks = [
            # Outer lip contour
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            # Inner lip contour  
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415,
            # Additional mouth corner points
            291, 303, 267, 269, 270, 267, 271, 272, 271, 272
        ]
        
        # Simplified mouth region (key points for bounding box)
        self.key_mouth_points = [61, 291, 13, 14, 269, 270, 267, 271]
        
    def detect_mouth_region(self, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        Detect mouth region in a frame
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            Tuple of (x, y, width, height) for mouth bounding box, or None if not detected
        """
        try:
            # Convert BGR to RGB for MediaPipe
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process frame
            results = self.face_mesh.process(rgb_frame)
            
            if not results.multi_face_landmarks:
                return None
                
            # Get first face landmarks
            face_landmarks = results.multi_face_landmarks[0]
            
            # Extract mouth landmark coordinates
            h, w = frame.shape[:2]
            mouth_points = []
            
            for idx in self.key_mouth_points:
                landmark = face_landmarks.landmark[idx]
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                mouth_points.append((x, y))
            
            if not mouth_points:
                return None
                
            # Calculate bounding box
            xs = [p[0] for p in mouth_points]
            ys = [p[1] for p in mouth_points]
            
            x_min, x_max = min(xs), max(xs)
            y_min, y_max = min(ys), max(ys)
            
            # Add margin
            width = x_max - x_min
            height = y_max - y_min
            
            margin_w = int(width * self.mouth_margin)
            margin_h = int(height * self.mouth_margin)
            
            x_min = max(0, x_min - margin_w)
            y_min = max(0, y_min - margin_h)
            x_max = min(w, x_max + margin_w)
            y_max = min(h, y_max + margin_h)
            
            return (x_min, y_min, x_max - x_min, y_max - y_min)
            
        except Exception as e:
            logger.warning(f"Mouth detection failed: {e}")
            return None
    
    def crop_mouth(self, frame: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """
        Crop mouth region from frame and resize to target size
        
        Args:
            frame: Input frame
            bbox: Bounding box (x, y, width, height)
            
        Returns:
            Cropped and resized mouth region
        """
        x, y, w, h = bbox
        
        # Crop mouth region
        mouth_crop = frame[y:y+h, x:x+w]
        
        # Resize to target size
        mouth_resized = cv2.resize(mouth_crop, self.mouth_crop_size, interpolation=cv2.INTER_AREA)
        
        return mouth_resized
    
    def process_frame(self, frame: np.ndarray) -> Optional[np.ndarray]:
        """
        Process a single frame: detect mouth and return cropped region
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            Cropped mouth region or None if detection failed
        """
        bbox = self.detect_mouth_region(frame)
        if bbox is None:
            return None
            
        return self.crop_mouth(frame, bbox)
    
    def process_video_frames(self, frames: List[np.ndarray]) -> List[np.ndarray]:
        """
        Process multiple video frames with mouth detection and cropping
        
        Args:
            frames: List of video frames
            
        Returns:
            List of cropped mouth regions (may be shorter if some detections fail)
        """
        mouth_frames = []
        
        for i, frame in enumerate(frames):
            mouth_crop = self.process_frame(frame)
            if mouth_crop is not None:
                mouth_frames.append(mouth_crop)
            else:
                logger.debug(f"Mouth detection failed for frame {i}")
        
        return mouth_frames
    
    def __del__(self):
        """Cleanup MediaPipe resources"""
        if hasattr(self, 'face_mesh'):
            self.face_mesh.close()


def test_mouth_detector():
    """Test mouth detector with a sample video"""
    import os
    from pathlib import Path

    # Find a test video - check multiple possible locations
    test_video_paths = [
        Path("test_videos"),
        Path("test_webm_videos"),
        Path("mouth_cropped_videos"),
        Path("data/where_am_i")
    ]

    test_videos = []
    for test_dir in test_video_paths:
        if test_dir.exists():
            videos = list(test_dir.glob("*.webm"))
            if videos:
                test_videos = videos
                break

    if not test_videos:
        print("❌ No test videos found in any directory")
        return
    
    test_video = test_videos[0]
    print(f"🧪 Testing mouth detector with: {test_video.name}")
    
    # Initialize detector
    detector = MouthDetector()
    
    # Open video
    cap = cv2.VideoCapture(str(test_video))
    if not cap.isOpened():
        print("❌ Failed to open video")
        return
    
    frame_count = 0
    success_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        frame_count += 1
        mouth_crop = detector.process_frame(frame)
        
        if mouth_crop is not None:
            success_count += 1
            
        # Test first 10 frames
        if frame_count >= 10:
            break
    
    cap.release()
    
    success_rate = success_count / frame_count if frame_count > 0 else 0
    print(f"✅ Mouth detection success rate: {success_rate:.1%} ({success_count}/{frame_count})")
    
    if success_rate > 0.5:
        print("🎉 Mouth detector working well!")
    else:
        print("⚠️  Low success rate - may need parameter tuning")


if __name__ == "__main__":
    test_mouth_detector()
