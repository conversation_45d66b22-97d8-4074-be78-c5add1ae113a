"""
Video processing utilities for lightweight VSR
Handles frame extraction, preprocessing, and augmentation with mouth detection
"""

import cv2
import numpy as np
import torch
from typing import List, Tuple, Optional, Union
import av
from pathlib import Path
import random
import logging

# Import mouth detector
from .mouth_detector import MouthDetector

logger = logging.getLogger(__name__)


class VideoProcessor:
    """Enhanced video preprocessing pipeline for mouth-focused videos with 64-frame temporal sampling"""

    # Dataset-specific normalization constants (computed from Perfect 10 training data)
    DATASET_MEAN = 0.578564  # Computed from Perfect 10 training data
    DATASET_STD = 0.141477   # Computed from Perfect 10 training data

    def __init__(self,
                 target_frames: int = 64,  # Enhanced: 64 frames instead of 32
                 target_size: Tuple[int, int] = (112, 112),  # Enhanced: 112x112 instead of 96x96
                 grayscale: bool = True,
                 fps: Optional[float] = 25.0,  # Enhanced: Standardize to 25 FPS
                 mouth_crop: Optional[Tuple[int, int, int, int]] = None,
                 use_dataset_normalization: bool = True,  # Enhanced: z-score normalization
                 use_mouth_detection: bool = True,  # NEW: Enable automatic mouth detection
                 mouth_detection_fallback: bool = True):  # NEW: Fallback to center crop if detection fails
        self.target_frames = target_frames
        self.target_size = target_size
        self.grayscale = grayscale
        self.fps = fps
        self.mouth_crop = mouth_crop  # (x, y, width, height) for mouth region cropping
        self.use_dataset_normalization = use_dataset_normalization
        self.use_mouth_detection = use_mouth_detection
        self.mouth_detection_fallback = mouth_detection_fallback

        # Initialize mouth detector if enabled
        self.mouth_detector = None
        if self.use_mouth_detection:
            try:
                self.mouth_detector = MouthDetector(
                    mouth_crop_size=self.target_size,
                    mouth_margin=0.3,
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                logger.info("Mouth detector initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize mouth detector: {e}")
                if not self.mouth_detection_fallback:
                    raise
    
    def extract_frames(self, video_path: Union[str, Path]) -> np.ndarray:
        """
        Enhanced frame extraction with FPS normalization to 25 FPS

        Args:
            video_path: Path to video file

        Returns:
            frames: numpy array of shape (frames, height, width, channels)
        """
        video_path = str(video_path)

        # Always use OpenCV for better FPS control and reliability
        return self._extract_frames_opencv_enhanced(video_path)
    
    def _extract_frames_opencv_enhanced(self, video_path: str) -> np.ndarray:
        """Enhanced frame extraction with FPS normalization using OpenCV"""
        cap = cv2.VideoCapture(video_path)

        # Get original video properties
        original_fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        if original_fps <= 0:
            original_fps = 25.0  # Default fallback

        frames = []

        # Calculate frame sampling strategy for FPS normalization
        if self.fps and abs(original_fps - self.fps) > 0.1:
            # Need to resample to target FPS
            frame_ratio = original_fps / self.fps
            target_frame_indices = []

            for i in range(int(total_frames / frame_ratio)):
                frame_idx = int(i * frame_ratio)
                if frame_idx < total_frames:
                    target_frame_indices.append(frame_idx)

            # Extract specific frames
            for frame_idx in target_frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    if self.grayscale:
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                        frame = np.expand_dims(frame, axis=-1)
                    else:
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frames.append(frame)
        else:
            # Extract all frames (no FPS conversion needed)
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if self.grayscale:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    frame = np.expand_dims(frame, axis=-1)
                else:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                frames.append(frame)

        cap.release()

        if not frames:
            raise ValueError(f"No frames extracted from {video_path}")

        return np.array(frames)

    def _extract_frames_opencv(self, video_path: str) -> np.ndarray:
        """Legacy frame extraction using OpenCV (kept for compatibility)"""
        return self._extract_frames_opencv_enhanced(video_path)

    def apply_mouth_crop(self, frames: np.ndarray) -> np.ndarray:
        """Apply mouth region cropping if specified"""
        if self.mouth_crop is None:
            return frames

        x, y, w, h = self.mouth_crop
        cropped_frames = []

        for frame in frames:
            # Ensure crop region is within frame bounds
            frame_h, frame_w = frame.shape[:2]

            # Adjust crop region if it exceeds frame bounds
            x_end = min(x + w, frame_w)
            y_end = min(y + h, frame_h)
            x_start = max(0, x)
            y_start = max(0, y)

            # Apply crop
            if len(frame.shape) == 3:
                cropped = frame[y_start:y_end, x_start:x_end, :]
            else:
                cropped = frame[y_start:y_end, x_start:x_end]

            cropped_frames.append(cropped)

        return np.array(cropped_frames)

    def apply_mouth_detection(self, frames: np.ndarray) -> np.ndarray:
        """Apply automatic mouth detection and cropping to frames"""
        if not self.use_mouth_detection or self.mouth_detector is None:
            return frames

        mouth_cropped_frames = []
        detection_failures = 0

        for i, frame in enumerate(frames):
            # Convert grayscale back to BGR for mouth detection
            if len(frame.shape) == 3 and frame.shape[2] == 1:
                # Grayscale with channel dimension
                frame_bgr = cv2.cvtColor(frame.squeeze(-1), cv2.COLOR_GRAY2BGR)
            elif len(frame.shape) == 2:
                # Grayscale without channel dimension
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
            elif len(frame.shape) == 3 and frame.shape[2] == 3:
                # RGB to BGR
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame

            # Detect and crop mouth
            mouth_crop = self.mouth_detector.process_frame(frame_bgr)

            if mouth_crop is not None:
                # Convert back to original format and ensure consistent shape
                if self.grayscale:
                    if len(mouth_crop.shape) == 3:
                        mouth_crop = cv2.cvtColor(mouth_crop, cv2.COLOR_BGR2GRAY)
                    # Ensure target size
                    mouth_crop = cv2.resize(mouth_crop, self.target_size, interpolation=cv2.INTER_AREA)
                    # Add channel dimension for consistency
                    mouth_crop = np.expand_dims(mouth_crop, axis=-1)
                else:
                    if len(mouth_crop.shape) == 3:
                        mouth_crop = cv2.cvtColor(mouth_crop, cv2.COLOR_BGR2RGB)
                    # Ensure target size
                    mouth_crop = cv2.resize(mouth_crop, self.target_size, interpolation=cv2.INTER_AREA)

                mouth_cropped_frames.append(mouth_crop)
            else:
                detection_failures += 1

                # Fallback strategy
                if self.mouth_detection_fallback:
                    # Use center crop as fallback
                    h, w = frame.shape[:2]
                    crop_size = min(h, w) // 2
                    center_x, center_y = w // 2, h // 2

                    x1 = max(0, center_x - crop_size // 2)
                    y1 = max(0, center_y - crop_size // 2)
                    x2 = min(w, x1 + crop_size)
                    y2 = min(h, y1 + crop_size)

                    if len(frame.shape) == 3:
                        fallback_crop = frame[y1:y2, x1:x2, :]
                    else:
                        fallback_crop = frame[y1:y2, x1:x2]

                    # Resize to target size and ensure consistent format
                    fallback_crop = cv2.resize(fallback_crop, self.target_size, interpolation=cv2.INTER_AREA)

                    if self.grayscale and len(fallback_crop.shape) == 2:
                        fallback_crop = np.expand_dims(fallback_crop, axis=-1)

                    mouth_cropped_frames.append(fallback_crop)
                else:
                    logger.warning(f"Mouth detection failed for frame {i}, skipping")

        if detection_failures > 0:
            success_rate = (len(frames) - detection_failures) / len(frames)
            logger.info(f"Mouth detection success rate: {success_rate:.1%} ({len(frames) - detection_failures}/{len(frames)})")

        if not mouth_cropped_frames:
            raise ValueError("No frames could be processed with mouth detection")

        # Ensure all frames have consistent shape
        processed_frames = []
        target_shape = None

        for frame in mouth_cropped_frames:
            if target_shape is None:
                target_shape = frame.shape

            # Ensure frame matches target shape
            if frame.shape != target_shape:
                if self.grayscale:
                    if len(frame.shape) == 2:
                        frame = np.expand_dims(frame, axis=-1)
                    frame = cv2.resize(frame.squeeze(-1), self.target_size, interpolation=cv2.INTER_AREA)
                    frame = np.expand_dims(frame, axis=-1)
                else:
                    frame = cv2.resize(frame, self.target_size, interpolation=cv2.INTER_AREA)

            processed_frames.append(frame)

        return np.array(processed_frames)

    def resize_frames(self, frames: np.ndarray) -> np.ndarray:
        """Enhanced resize frames to target size with bicubic interpolation"""
        resized_frames = []

        for frame in frames:
            if self.grayscale and len(frame.shape) == 3:
                # Handle grayscale frames with channel dimension
                if frame.shape[2] == 1:
                    # Use bicubic interpolation for better quality
                    resized = cv2.resize(frame[:, :, 0], self.target_size, interpolation=cv2.INTER_CUBIC)
                else:
                    # Convert to grayscale first
                    gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
                    resized = cv2.resize(gray, self.target_size, interpolation=cv2.INTER_CUBIC)
                resized = np.expand_dims(resized, axis=-1)
            elif self.grayscale and len(frame.shape) == 2:
                # Already grayscale, just resize with bicubic interpolation
                resized = cv2.resize(frame, self.target_size, interpolation=cv2.INTER_CUBIC)
                resized = np.expand_dims(resized, axis=-1)
            else:
                # Color frames with bicubic interpolation
                resized = cv2.resize(frame, self.target_size, interpolation=cv2.INTER_CUBIC)
            resized_frames.append(resized)

        return np.array(resized_frames)
    
    def temporal_crop_or_pad(self, frames: np.ndarray) -> np.ndarray:
        """Enhanced temporal sampling with uniform downsampling for 64-frame target"""
        num_frames = len(frames)

        if num_frames == self.target_frames:
            return frames
        elif num_frames > self.target_frames:
            # Enhanced: Use uniform temporal downsampling with np.linspace
            indices = np.linspace(0, num_frames - 1, self.target_frames, dtype=int)
            return frames[indices]
        else:
            # Enhanced: Pad by repeating the last valid frame
            sampled_frames = list(frames)
            while len(sampled_frames) < self.target_frames:
                if sampled_frames:
                    sampled_frames.append(sampled_frames[-1])  # Repeat last frame
                else:
                    # Edge case: no frames available, create appropriate black frame
                    if len(frames) > 0:
                        black_frame = np.zeros_like(frames[0])
                    else:
                        if self.grayscale:
                            black_frame = np.zeros((*self.target_size, 1), dtype=np.uint8)
                        else:
                            black_frame = np.zeros((*self.target_size, 3), dtype=np.uint8)
                    sampled_frames.append(black_frame)
            return np.array(sampled_frames[:self.target_frames])
    
    def normalize(self, frames: np.ndarray) -> np.ndarray:
        """Enhanced normalization with dataset-specific z-score normalization"""
        frames_float = frames.astype(np.float32)

        if self.use_dataset_normalization:
            # Dataset-specific z-score normalization
            # Convert to [0,1] first, then apply z-score
            frames_normalized = frames_float / 255.0
            return (frames_normalized - self.DATASET_MEAN) / self.DATASET_STD
        else:
            # Legacy [0,1] normalization for backward compatibility
            return frames_float / 255.0
    
    def process_video(self, video_path: Union[str, Path]) -> torch.Tensor:
        """
        Complete video processing pipeline
        
        Args:
            video_path: Path to video file
            
        Returns:
            Processed video tensor of shape (1, frames, height, width) for grayscale
            or (3, frames, height, width) for RGB
        """
        # Extract frames
        frames = self.extract_frames(video_path)

        # Apply automatic mouth detection and cropping (NEW)
        if self.use_mouth_detection:
            frames = self.apply_mouth_detection(frames)
        else:
            # Apply manual mouth cropping if specified
            frames = self.apply_mouth_crop(frames)
            # Resize frames (only needed if not using mouth detection)
            frames = self.resize_frames(frames)
        
        # Temporal crop/pad
        frames = self.temporal_crop_or_pad(frames)
        
        # Normalize
        frames = self.normalize(frames)
        
        # Convert to tensor and rearrange dimensions
        # From (T, H, W, C) to (C, T, H, W)
        frames_tensor = torch.from_numpy(frames).permute(3, 0, 1, 2)
        
        return frames_tensor


class VideoAugmentation:
    """Data augmentation for video training"""
    
    def __init__(self,
                 brightness_range: float = 0.15,
                 contrast_range: float = 0.15,
                 scale_range: float = 0.10,
                 vertical_jitter: int = 8,
                 temporal_jitter: int = 4):
        self.brightness_range = brightness_range
        self.contrast_range = contrast_range
        self.scale_range = scale_range
        self.vertical_jitter = vertical_jitter
        self.temporal_jitter = temporal_jitter
    
    def apply_brightness_contrast(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random brightness and contrast changes"""
        if random.random() < 0.5:
            # Brightness
            brightness_factor = 1.0 + random.uniform(-self.brightness_range, self.brightness_range)
            frames = frames * brightness_factor
            
            # Contrast
            contrast_factor = 1.0 + random.uniform(-self.contrast_range, self.contrast_range)
            mean = frames.mean()
            frames = (frames - mean) * contrast_factor + mean
            
            # Clamp to valid range
            frames = torch.clamp(frames, 0.0, 1.0)
        
        return frames
    
    def apply_scale_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random scaling"""
        if random.random() < 0.5:
            scale_factor = 1.0 + random.uniform(-self.scale_range, self.scale_range)
            
            # Get current size
            _, T, H, W = frames.shape
            new_H, new_W = int(H * scale_factor), int(W * scale_factor)
            
            # Resize
            frames = torch.nn.functional.interpolate(
                frames.unsqueeze(0), 
                size=(T, new_H, new_W), 
                mode='trilinear', 
                align_corners=False
            ).squeeze(0)
            
            # Crop or pad back to original size
            if new_H > H or new_W > W:
                # Crop from center
                start_h = (new_H - H) // 2
                start_w = (new_W - W) // 2
                frames = frames[:, :, start_h:start_h+H, start_w:start_w+W]
            elif new_H < H or new_W < W:
                # Pad
                pad_h = (H - new_H) // 2
                pad_w = (W - new_W) // 2
                frames = torch.nn.functional.pad(
                    frames,
                    (pad_w, W-new_W-pad_w, pad_h, H-new_H-pad_h),
                    mode='replicate'
                )
        
        return frames
    
    def apply_vertical_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random vertical translation"""
        if random.random() < 0.5:
            shift = random.randint(-self.vertical_jitter, self.vertical_jitter)
            if shift != 0:
                _, T, H, W = frames.shape
                if shift > 0:
                    # Shift down
                    frames = torch.cat([
                        frames[:, :, shift:, :],
                        frames[:, :, -shift:, :].repeat(1, 1, shift, 1)
                    ], dim=2)
                else:
                    # Shift up
                    shift = abs(shift)
                    frames = torch.cat([
                        frames[:, :, :shift, :].repeat(1, 1, shift, 1),
                        frames[:, :, :-shift, :]
                    ], dim=2)
        
        return frames
    
    def apply_temporal_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random temporal shifts"""
        if random.random() < 0.5:
            shift = random.randint(-self.temporal_jitter, self.temporal_jitter)
            if shift != 0:
                C, T, H, W = frames.shape
                if shift > 0:
                    # Shift forward in time
                    frames = torch.cat([
                        frames[:, shift:, :, :],
                        frames[:, -shift:, :, :].repeat(1, shift, 1, 1)
                    ], dim=1)
                else:
                    # Shift backward in time
                    shift = abs(shift)
                    frames = torch.cat([
                        frames[:, :shift, :, :].repeat(1, shift, 1, 1),
                        frames[:, :-shift, :, :]
                    ], dim=1)
        
        return frames
    
    def __call__(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply all augmentations"""
        frames = self.apply_brightness_contrast(frames)
        frames = self.apply_scale_jitter(frames)
        frames = self.apply_vertical_jitter(frames)
        frames = self.apply_temporal_jitter(frames)
        return frames


def create_video_processor(config: dict) -> VideoProcessor:
    """Factory function to create VideoProcessor from config"""
    return VideoProcessor(
        target_frames=config.get('frames', 32),
        target_size=(config.get('height', 96), config.get('width', 96)),
        grayscale=config.get('grayscale', True),
        use_mouth_detection=config.get('use_mouth_detection', True),
        mouth_detection_fallback=config.get('mouth_detection_fallback', True)
    )


def create_augmentation(config: dict) -> VideoAugmentation:
    """Factory function to create VideoAugmentation from config"""
    return VideoAugmentation(
        brightness_range=config.get('brightness_contrast_range', 0.15),
        contrast_range=config.get('brightness_contrast_range', 0.15),
        scale_range=config.get('scale_range', 0.10),
        vertical_jitter=config.get('vertical_jitter', 8),
        temporal_jitter=config.get('temporal_jitter', 4)
    )


if __name__ == "__main__":
    # Test video processing
    processor = VideoProcessor()

    # Test with a sample video (if available)
    test_video = "test_webm_videos/sample.mp4"
    if Path(test_video).exists():
        try:
            frames = processor.process_video(test_video)
            print(f"Processed video shape: {frames.shape}")
            print(f"Frame range: [{frames.min():.3f}, {frames.max():.3f}]")
        except Exception as e:
            print(f"Error processing video: {e}")
    else:
        print("No test video found, skipping test")
