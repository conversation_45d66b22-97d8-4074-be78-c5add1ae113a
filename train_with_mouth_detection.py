#!/usr/bin/env python3
"""
Train the lightweight VSR model with mouth-detected preprocessed dataset
Expected to achieve significantly better accuracy than the baseline 10.7%
"""

import os
import sys
import json
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import logging
from tqdm import tqdm

# Add backend to path
sys.path.append('backend')

from backend.lightweight_vsr.model import Mobile3DTiny

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreprocessedDataset(Dataset):
    """Dataset for preprocessed mouth-detected tensors"""
    
    def __init__(self, manifest_path, split='train', train_ratio=0.7, val_ratio=0.15):
        with open(manifest_path) as f:
            self.manifest = json.load(f)
        
        # Create phrase to label mapping
        phrases = sorted(set(item['phrase'] for item in self.manifest))
        self.phrase_to_label = {phrase: idx for idx, phrase in enumerate(phrases)}
        self.label_to_phrase = {idx: phrase for phrase, idx in self.phrase_to_label.items()}
        self.num_classes = len(phrases)
        
        # Split data
        np.random.seed(42)
        indices = np.random.permutation(len(self.manifest))
        
        train_end = int(len(indices) * train_ratio)
        val_end = int(len(indices) * (train_ratio + val_ratio))
        
        if split == 'train':
            self.indices = indices[:train_end]
        elif split == 'val':
            self.indices = indices[train_end:val_end]
        else:  # test
            self.indices = indices[val_end:]
        
        logger.info(f"{split} split: {len(self.indices)} samples")
        
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        item = self.manifest[self.indices[idx]]

        # Load preprocessed tensor
        tensor = torch.load(item['preprocessed_path'])
        label = self.phrase_to_label[item['phrase']]

        # Ensure correct shape: (C, T, H, W) where C=1 for grayscale
        if tensor.dim() == 4 and tensor.shape[0] == 1:
            # Already correct shape (1, T, H, W)
            return tensor, label
        elif tensor.dim() == 5:
            # Remove batch dimension: (B, C, T, H, W) -> (C, T, H, W)
            return tensor.squeeze(0), label
        else:
            raise ValueError(f"Unexpected tensor shape: {tensor.shape}")

def train_model():
    """Train the model with mouth-detected dataset"""
    print("🚀 Training Lightweight VSR with Mouth Detection")
    print("=" * 60)
    
    # Configuration
    config = {
        "batch_size": 8,
        "learning_rate": 0.001,
        "epochs": 8,
        "device": "cpu",  # CPU training for compatibility
        "save_dir": "artifacts/mouth_detected_v1"
    }
    
    # Create save directory
    save_dir = Path(config["save_dir"])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Load dataset
    manifest_path = "preprocessed_mouth_detected/processed_manifest.json"
    if not Path(manifest_path).exists():
        print("❌ Preprocessed manifest not found. Run reprocessing first.")
        return False
    
    print("📊 Loading preprocessed dataset...")
    train_dataset = PreprocessedDataset(manifest_path, split='train')
    val_dataset = PreprocessedDataset(manifest_path, split='val')
    test_dataset = PreprocessedDataset(manifest_path, split='test')
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=config["batch_size"], shuffle=False)
    
    print(f"📈 Dataset splits: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    print(f"🏷️  Number of classes: {train_dataset.num_classes}")
    
    # Initialize model
    print("🔧 Initializing Mobile3DTiny model...")
    model = Mobile3DTiny(num_classes=train_dataset.num_classes)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=config["learning_rate"])
    
    # Training metrics
    training_history = {
        "train_loss": [],
        "train_acc": [],
        "val_loss": [],
        "val_acc": [],
        "val_f1": []
    }
    
    best_val_acc = 0.0
    start_time = time.time()
    
    print(f"\n🎯 Starting training for {config['epochs']} epochs...")
    
    for epoch in range(config["epochs"]):
        epoch_start = time.time()
        
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['epochs']} [Train]")
        for batch_idx, (data, target) in enumerate(train_pbar):
            optimizer.zero_grad()
            
            # Forward pass
            output = model(data)
            loss = criterion(output, target)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Statistics
            train_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            train_total += target.size(0)
            train_correct += (predicted == target).sum().item()
            
            # Update progress bar
            train_acc = 100. * train_correct / train_total
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{train_acc:.1f}%'
            })
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{config['epochs']} [Val]")
            for data, target in val_pbar:
                output = model(data)
                loss = criterion(output, target)
                
                val_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                val_total += target.size(0)
                val_correct += (predicted == target).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
                
                val_acc = 100. * val_correct / val_total
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{val_acc:.1f}%'
                })
        
        # Calculate metrics
        epoch_train_loss = train_loss / len(train_loader)
        epoch_train_acc = 100. * train_correct / train_total
        epoch_val_loss = val_loss / len(val_loader)
        epoch_val_acc = 100. * val_correct / val_total
        epoch_val_f1 = f1_score(all_targets, all_predictions, average='macro') * 100
        
        # Save metrics
        training_history["train_loss"].append(epoch_train_loss)
        training_history["train_acc"].append(epoch_train_acc)
        training_history["val_loss"].append(epoch_val_loss)
        training_history["val_acc"].append(epoch_val_acc)
        training_history["val_f1"].append(epoch_val_f1)
        
        epoch_time = time.time() - epoch_start
        
        print(f"\nEpoch {epoch+1}/{config['epochs']} ({epoch_time:.1f}s):")
        print(f"  Train Loss: {epoch_train_loss:.4f}, Train Acc: {epoch_train_acc:.1f}%")
        print(f"  Val Loss: {epoch_val_loss:.4f}, Val Acc: {epoch_val_acc:.1f}%, Val F1: {epoch_val_f1:.1f}%")
        
        # Save best model
        if epoch_val_acc > best_val_acc:
            best_val_acc = epoch_val_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': epoch_val_acc,
                'val_f1': epoch_val_f1,
                'phrase_to_label': train_dataset.phrase_to_label,
                'label_to_phrase': train_dataset.label_to_phrase
            }, save_dir / "best_model.pth")
            print(f"  ✅ New best model saved! (Val Acc: {best_val_acc:.1f}%)")
    
    total_time = time.time() - start_time
    
    # Final evaluation on test set
    print(f"\n🧪 Final evaluation on test set...")
    model.eval()
    test_correct = 0
    test_total = 0
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(test_loader, desc="Testing"):
            output = model(data)
            _, predicted = torch.max(output.data, 1)
            test_total += target.size(0)
            test_correct += (predicted == target).sum().item()
            
            test_predictions.extend(predicted.cpu().numpy())
            test_targets.extend(target.cpu().numpy())
    
    test_acc = 100. * test_correct / test_total
    test_f1 = f1_score(test_targets, test_predictions, average='macro') * 100
    
    # Save final results
    results = {
        "training_config": config,
        "training_history": training_history,
        "final_results": {
            "best_val_acc": best_val_acc,
            "test_acc": test_acc,
            "test_f1": test_f1,
            "total_training_time": total_time
        },
        "dataset_info": {
            "num_classes": train_dataset.num_classes,
            "train_samples": len(train_dataset),
            "val_samples": len(val_dataset),
            "test_samples": len(test_dataset)
        }
    }
    
    with open(save_dir / "training_results.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print final summary
    print("\n" + "=" * 60)
    print("🎉 Training Complete!")
    print("=" * 60)
    print(f"⏱️  Total Training Time: {total_time:.1f} seconds")
    print(f"🎯 Best Validation Accuracy: {best_val_acc:.1f}%")
    print(f"🧪 Test Accuracy: {test_acc:.1f}%")
    print(f"📊 Test F1 Score: {test_f1:.1f}%")
    print(f"📁 Model saved to: {save_dir}")
    
    # Compare with baseline
    baseline_acc = 10.7  # Previous training result
    improvement = test_acc - baseline_acc
    
    print(f"\n📈 Improvement Analysis:")
    print(f"   Baseline (no mouth detection): {baseline_acc:.1f}%")
    print(f"   With mouth detection: {test_acc:.1f}%")
    print(f"   Improvement: {improvement:+.1f} percentage points")
    
    if test_acc > baseline_acc * 2:  # More than 2x improvement
        print("🚀 EXCELLENT: More than 2x improvement!")
        return True
    elif test_acc > baseline_acc + 10:  # More than 10% improvement
        print("✅ GOOD: Significant improvement achieved!")
        return True
    elif test_acc > baseline_acc:
        print("👍 POSITIVE: Some improvement achieved")
        return True
    else:
        print("⚠️  No improvement over baseline")
        return False

if __name__ == "__main__":
    success = train_model()
    sys.exit(0 if success else 1)
