#!/usr/bin/env python3
"""
Train with a focused subset of 10 phrases for better accuracy
Use the proven training pipeline with a smaller, more manageable dataset
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def create_focused_config():
    """Create a focused training configuration for 10 phrases"""
    
    # Select 10 most distinct phrases for better classification
    focused_phrases = [
        "where am i",
        "i need help", 
        "i m in pain",
        "i feel anxious",
        "my chest hurts",
        "i need water",
        "call the nurse",
        "i want to see my husband",
        "what happened to me",
        "stay with me please"
    ]
    
    config = {
        "name": "focused_10_phrases",
        "phrases": focused_phrases,
        "max_videos_per_phrase": 10,  # Use up to 10 videos per phrase
        "frames": 32,
        "height": 96,
        "width": 96,
        "grayscale": True,
        "batch_size": 8,
        "epochs": 10,
        "learning_rate": 0.001
    }
    
    return config

def create_focused_manifest(config):
    """Create a manifest with only the focused phrases"""
    import pandas as pd
    
    print("📋 Creating focused manifest...")
    
    # Load original manifest
    original_manifest = Path("data/manifest.csv")
    if not original_manifest.exists():
        print("❌ Original manifest not found")
        return False
    
    df = pd.read_csv(original_manifest)
    
    # Filter for focused phrases
    focused_df = df[df['phrase'].isin(config['phrases'])]
    
    # Limit videos per phrase
    if config['max_videos_per_phrase']:
        focused_df = focused_df.groupby('phrase').head(config['max_videos_per_phrase'])
    
    print(f"📊 Focused dataset: {len(focused_df)} videos across {len(config['phrases'])} phrases")
    
    # Save focused manifest
    focused_manifest_path = Path("data/focused_10_phrases_manifest.csv")
    focused_df.to_csv(focused_manifest_path, index=False)
    
    # Print phrase distribution
    phrase_counts = focused_df['phrase'].value_counts()
    print("\n📝 Phrase distribution:")
    for phrase, count in phrase_counts.items():
        print(f"  {phrase}: {count} videos")
    
    return focused_manifest_path

def train_focused_model():
    """Train the focused model using the proven training pipeline"""
    print("🚀 Training Focused 10-Phrase Model")
    print("=" * 50)
    
    config = create_focused_config()
    
    # Create focused manifest
    manifest_path = create_focused_manifest(config)
    if not manifest_path:
        return False
    
    # Create training configuration file
    config_content = f"""
# Focused 10-phrase training configuration
name: {config['name']}
data:
  manifest_path: {manifest_path}
  frames: {config['frames']}
  height: {config['height']}
  width: {config['width']}
  grayscale: {config['grayscale']}

model:
  name: Mobile3DTiny
  num_classes: {len(config['phrases'])}

training:
  batch_size: {config['batch_size']}
  epochs: {config['epochs']}
  learning_rate: {config['learning_rate']}
  device: cpu

output:
  checkpoint_dir: checkpoints/{config['name']}
  log_dir: logs/{config['name']}
  artifacts_dir: artifacts/{config['name']}
"""
    
    config_path = Path(f"configs/{config['name']}.yaml")
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print(f"📝 Configuration saved to: {config_path}")
    
    # Run training using the proven training script
    print("\n🎯 Starting focused training...")
    
    try:
        # Use the existing training script with our focused config
        cmd = [
            sys.executable, 
            "backend/lightweight_vsr/train.py",
            "--config", str(config_path),
            "--manifest", str(manifest_path)
        ]
        
        print(f"🔧 Running command: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
        training_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ Training completed successfully in {training_time:.1f} seconds!")
            print("\n📊 Training output:")
            print(result.stdout)
            
            # Check for artifacts
            artifacts_dir = Path(f"artifacts/{config['name']}")
            if artifacts_dir.exists():
                artifacts = list(artifacts_dir.glob("*"))
                print(f"\n📁 Generated artifacts ({len(artifacts)} files):")
                for artifact in artifacts[:5]:  # Show first 5
                    print(f"  ✅ {artifact.name}")
                if len(artifacts) > 5:
                    print(f"  ... and {len(artifacts) - 5} more files")
            
            return True
            
        else:
            print(f"❌ Training failed with return code {result.returncode}")
            print("Error output:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Training timed out after 30 minutes")
        return False
    except Exception as e:
        print(f"❌ Training error: {e}")
        return False

def main():
    """Main function"""
    print("🎯 Focused 10-Phrase Training Pipeline")
    print("=" * 60)
    
    success = train_focused_model()
    
    if success:
        print("\n🎉 Focused training completed successfully!")
        print("✅ Ready for backend integration and mobile testing")
    else:
        print("\n❌ Focused training failed")
        print("💡 Try adjusting the configuration or checking the data")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
