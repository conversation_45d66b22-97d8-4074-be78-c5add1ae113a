{"App Initialization": {"status": "passed", "duration": 0, "result": {"initialized": true, "checks": 3}, "timestamp": "2025-08-26T16:16:56.947Z"}, "Camera Permission Check": {"status": "passed", "duration": 0, "result": {"camera_permission": true, "microphone_permission": true, "simulated": true}, "timestamp": "2025-08-26T16:16:56.947Z"}, "Server Connection": {"status": "passed", "duration": 19, "result": {"status": "healthy", "vsr_impl": "lightweight", "model_loaded": true}, "timestamp": "2025-08-26T16:16:56.966Z"}, "Recording Preparation": {"status": "passed", "duration": 1, "result": {"preparations_completed": 4, "server_ready": true}, "timestamp": "2025-08-26T16:16:56.967Z"}, "Video Recording Simulation": {"status": "passed", "duration": 9605, "result": {"duration_ms": 9605, "progress_updates": 6, "simulated_file_size": 51200}, "timestamp": "2025-08-26T16:17:06.573Z"}, "Video Upload": {"status": "passed", "duration": 16, "result": {"upload_duration_ms": 13, "file_size": 262, "server_response": {"success": true, "prediction": "stay with me please", "confidence": 0.611129124382594, "model_type": "dummy_demo", "processing_time": 0.0014410018920898438, "file_size": 262}}, "timestamp": "2025-08-26T16:17:06.589Z"}, "Prediction Processing": {"status": "passed", "duration": 4, "result": {"total_requests": 5, "successful_predictions": 5, "success_rate": 100, "average_processing_time": 0.0011697769165039062}, "timestamp": "2025-08-26T16:17:06.593Z"}, "Confidence Evaluation": {"status": "passed", "duration": 0, "result": {"confidence_evaluations": [{"confidence": 0.8, "icu_mode_acceptable": true, "open_mode_acceptable": true, "thresholds_working": true}, {"confidence": 0.6, "icu_mode_acceptable": false, "open_mode_acceptable": true, "thresholds_working": true}, {"confidence": 0.4, "icu_mode_acceptable": false, "open_mode_acceptable": false, "thresholds_working": true}, {"confidence": 0.2, "icu_mode_acceptable": false, "open_mode_acceptable": false, "thresholds_working": true}]}, "timestamp": "2025-08-26T16:17:06.593Z"}, "Audio Feedback Preparation": {"status": "passed", "duration": 0, "result": {"audio_preparations": [{"phrase": "where am i", "tts_ready": true, "estimated_duration": 1.5}, {"phrase": "i need help", "tts_ready": true, "estimated_duration": 1.5}, {"phrase": "call the nurse", "tts_ready": true, "estimated_duration": 1.5}], "tts_system_ready": true}, "timestamp": "2025-08-26T16:17:06.593Z"}, "Complete Workflow": {"status": "passed", "duration": 0, "result": {"total_workflow_time": 16500, "validation_time": 0, "user_steps": 6, "estimated_user_experience": "excellent"}, "timestamp": "2025-08-26T16:17:06.593Z"}}