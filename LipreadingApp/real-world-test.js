#!/usr/bin/env node
/**
 * Comprehensive Real-World Testing Script for ICU Lipreading App
 * Tests performance, stability, and reliability under real conditions
 */

const API_BASE_URL = 'http://*************:8000';
const fs = require('fs');
const path = require('path');

class LipreadingTester {
    constructor() {
        this.testResults = {
            startTime: new Date(),
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                warnings: 0
            }
        };
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            'info': '📋',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'performance': '⚡'
        }[type] || '📋';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async runTest(testName, testFunction) {
        this.log(`Starting test: ${testName}`, 'info');
        const startTime = Date.now();
        
        try {
            const result = await testFunction();
            const duration = Date.now() - startTime;
            
            this.testResults.tests.push({
                name: testName,
                status: 'passed',
                duration,
                result,
                timestamp: new Date()
            });
            
            this.testResults.summary.passed++;
            this.log(`Test passed: ${testName} (${duration}ms)`, 'success');
            return result;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            
            this.testResults.tests.push({
                name: testName,
                status: 'failed',
                duration,
                error: error.message,
                timestamp: new Date()
            });
            
            this.testResults.summary.failed++;
            this.log(`Test failed: ${testName} - ${error.message}`, 'error');
            throw error;
        } finally {
            this.testResults.summary.total++;
        }
    }

    async testBasicConnectivity() {
        const response = await fetch(`${API_BASE_URL}/health`);
        if (!response.ok) {
            throw new Error(`Health check failed: ${response.status}`);
        }
        
        const data = await response.json();
        return {
            status: data.status,
            model_loaded: data.model_loaded,
            vsr_impl: data.vsr_impl
        };
    }

    async testPerformanceStats() {
        const response = await fetch(`${API_BASE_URL}/stats`);
        if (!response.ok) {
            throw new Error(`Stats endpoint failed: ${response.status}`);
        }
        
        const stats = await response.json();
        
        // Check for performance warnings
        if (stats.performance.average_processing_time > 5.0) {
            this.log(`High average processing time: ${stats.performance.average_processing_time.toFixed(2)}s`, 'warning');
            this.testResults.summary.warnings++;
        }
        
        if (stats.success_rate < 95) {
            this.log(`Low success rate: ${stats.success_rate.toFixed(1)}%`, 'warning');
            this.testResults.summary.warnings++;
        }
        
        return stats;
    }

    async testVideoUpload() {
        // Find a test video file
        const testVideoPath = '../test_webm_video.webm';
        
        if (!fs.existsSync(testVideoPath)) {
            throw new Error('Test video file not found');
        }
        
        const formData = new FormData();
        const videoBuffer = fs.readFileSync(testVideoPath);
        const blob = new Blob([videoBuffer], { type: 'video/webm' });
        formData.append('file', blob, 'test_video.webm');
        
        const startTime = Date.now();
        const response = await fetch(`${API_BASE_URL}/predict`, {
            method: 'POST',
            body: formData
        });
        const uploadTime = Date.now() - startTime;
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Upload failed: ${response.status} - ${errorText}`);
        }
        
        const result = await response.json();
        
        // Performance checks
        if (uploadTime > 10000) {
            this.log(`Slow upload detected: ${uploadTime}ms`, 'warning');
            this.testResults.summary.warnings++;
        }
        
        return {
            prediction: result.prediction,
            confidence: result.confidence,
            upload_time: uploadTime,
            processing_time: result.processing_time,
            file_size: result.file_size
        };
    }

    async testStressLoad() {
        this.log('Starting stress test with multiple concurrent requests...', 'performance');
        
        const concurrentRequests = 3;
        const promises = [];
        
        for (let i = 0; i < concurrentRequests; i++) {
            promises.push(this.testVideoUpload());
        }
        
        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        if (failed > 0) {
            this.log(`${failed}/${concurrentRequests} concurrent requests failed`, 'warning');
            this.testResults.summary.warnings++;
        }
        
        return {
            total: concurrentRequests,
            successful,
            failed,
            success_rate: (successful / concurrentRequests) * 100
        };
    }

    async testNetworkStability() {
        this.log('Testing network stability with repeated requests...', 'performance');
        
        const iterations = 5;
        const results = [];
        
        for (let i = 0; i < iterations; i++) {
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE_URL}/test-connection`);
                const responseTime = Date.now() - startTime;
                
                if (!response.ok) {
                    throw new Error(`Request ${i + 1} failed: ${response.status}`);
                }
                
                results.push(responseTime);
                
                // Wait between requests
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                this.log(`Network stability test iteration ${i + 1} failed: ${error.message}`, 'warning');
                this.testResults.summary.warnings++;
            }
        }
        
        const avgResponseTime = results.reduce((a, b) => a + b, 0) / results.length;
        const maxResponseTime = Math.max(...results);
        
        if (avgResponseTime > 1000) {
            this.log(`High average response time: ${avgResponseTime.toFixed(0)}ms`, 'warning');
            this.testResults.summary.warnings++;
        }
        
        return {
            iterations: results.length,
            average_response_time: avgResponseTime,
            max_response_time: maxResponseTime,
            all_response_times: results
        };
    }

    async runAllTests() {
        this.log('🚀 Starting Comprehensive Real-World Testing', 'info');
        this.log('=' * 60, 'info');
        
        try {
            // Basic connectivity
            await this.runTest('Basic Connectivity', () => this.testBasicConnectivity());
            
            // Performance stats
            await this.runTest('Performance Stats', () => this.testPerformanceStats());
            
            // Video upload
            await this.runTest('Video Upload & Prediction', () => this.testVideoUpload());
            
            // Network stability
            await this.runTest('Network Stability', () => this.testNetworkStability());
            
            // Stress testing
            await this.runTest('Stress Load Testing', () => this.testStressLoad());
            
        } catch (error) {
            this.log(`Critical test failure: ${error.message}`, 'error');
        }
        
        this.generateReport();
    }

    generateReport() {
        const duration = Date.now() - this.testResults.startTime.getTime();
        
        this.log('', 'info');
        this.log('📊 TEST RESULTS SUMMARY', 'info');
        this.log('=' * 60, 'info');
        this.log(`Total Tests: ${this.testResults.summary.total}`, 'info');
        this.log(`Passed: ${this.testResults.summary.passed}`, 'success');
        this.log(`Failed: ${this.testResults.summary.failed}`, this.testResults.summary.failed > 0 ? 'error' : 'info');
        this.log(`Warnings: ${this.testResults.summary.warnings}`, this.testResults.summary.warnings > 0 ? 'warning' : 'info');
        this.log(`Total Duration: ${duration}ms`, 'performance');
        
        // Save detailed report
        const reportPath = 'real-world-test-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
        this.log(`Detailed report saved to: ${reportPath}`, 'info');
        
        // Overall assessment
        const overallSuccess = this.testResults.summary.failed === 0;
        const productionReady = overallSuccess && this.testResults.summary.warnings <= 2;
        
        this.log('', 'info');
        this.log('🎯 PRODUCTION READINESS ASSESSMENT', 'info');
        this.log('=' * 60, 'info');
        
        if (productionReady) {
            this.log('✅ APP IS PRODUCTION READY', 'success');
            this.log('The lipreading app passed all critical tests and is ready for deployment.', 'success');
        } else if (overallSuccess) {
            this.log('⚠️  APP NEEDS OPTIMIZATION', 'warning');
            this.log('The app works but has performance issues that should be addressed.', 'warning');
        } else {
            this.log('❌ APP NOT READY FOR PRODUCTION', 'error');
            this.log('Critical issues found that must be fixed before deployment.', 'error');
        }
    }
}

// Run the tests
if (require.main === module) {
    const tester = new LipreadingTester();
    tester.runAllTests().catch(error => {
        console.error('Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = LipreadingTester;
