#!/usr/bin/env node
/**
 * Network Stability Testing for ICU Lipreading App
 * Tests connection stability under various conditions and extended use
 */

const API_BASE_URL = 'http://*************:8000';
const fs = require('fs');

class NetworkStabilityTester {
    constructor() {
        this.testResults = {
            startTime: new Date(),
            tests: [],
            networkMetrics: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                timeouts: 0,
                averageResponseTime: 0,
                maxResponseTime: 0,
                minResponseTime: Infinity,
                responseTimes: []
            }
        };
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            'info': '🌐',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'network': '📡'
        }[type] || '🌐';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async makeRequest(url, options = {}, timeout = 5000) {
        const startTime = Date.now();
        this.testResults.networkMetrics.totalRequests++;
        
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);
            
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            const responseTime = Date.now() - startTime;
            
            // Update metrics
            this.testResults.networkMetrics.responseTimes.push(responseTime);
            this.testResults.networkMetrics.maxResponseTime = Math.max(
                this.testResults.networkMetrics.maxResponseTime, 
                responseTime
            );
            this.testResults.networkMetrics.minResponseTime = Math.min(
                this.testResults.networkMetrics.minResponseTime, 
                responseTime
            );
            
            if (response.ok) {
                this.testResults.networkMetrics.successfulRequests++;
            } else {
                this.testResults.networkMetrics.failedRequests++;
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return { response, responseTime };
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            
            if (error.name === 'AbortError') {
                this.testResults.networkMetrics.timeouts++;
                throw new Error(`Request timeout after ${timeout}ms`);
            } else {
                this.testResults.networkMetrics.failedRequests++;
                throw error;
            }
        }
    }

    async testBasicConnectivity() {
        this.log('Testing basic connectivity...', 'network');
        
        const { response, responseTime } = await this.makeRequest(`${API_BASE_URL}/health`);
        const data = await response.json();
        
        return {
            status: data.status,
            responseTime,
            serverReady: data.model_loaded
        };
    }

    async testExtendedConnectivity() {
        this.log('Testing extended connectivity (50 requests over 30 seconds)...', 'network');
        
        const totalRequests = 50;
        const testDuration = 30000; // 30 seconds
        const interval = testDuration / totalRequests;
        
        const results = [];
        let consecutiveFailures = 0;
        let maxConsecutiveFailures = 0;
        
        for (let i = 0; i < totalRequests; i++) {
            try {
                const { responseTime } = await this.makeRequest(`${API_BASE_URL}/test-connection`);
                results.push({ success: true, responseTime, attempt: i + 1 });
                consecutiveFailures = 0;
                
                // Log progress every 10 requests
                if ((i + 1) % 10 === 0) {
                    this.log(`Progress: ${i + 1}/${totalRequests} requests completed`, 'network');
                }
                
            } catch (error) {
                consecutiveFailures++;
                maxConsecutiveFailures = Math.max(maxConsecutiveFailures, consecutiveFailures);
                results.push({ success: false, error: error.message, attempt: i + 1 });
                
                this.log(`Request ${i + 1} failed: ${error.message}`, 'warning');
            }
            
            // Wait before next request
            if (i < totalRequests - 1) {
                await new Promise(resolve => setTimeout(resolve, interval));
            }
        }
        
        const successfulRequests = results.filter(r => r.success).length;
        const successRate = (successfulRequests / totalRequests) * 100;
        
        return {
            totalRequests,
            successfulRequests,
            failedRequests: totalRequests - successfulRequests,
            successRate,
            maxConsecutiveFailures,
            results
        };
    }

    async testConcurrentConnections() {
        this.log('Testing concurrent connections (10 simultaneous requests)...', 'network');
        
        const concurrentRequests = 10;
        const promises = [];
        
        for (let i = 0; i < concurrentRequests; i++) {
            promises.push(
                this.makeRequest(`${API_BASE_URL}/health`)
                    .then(result => ({ success: true, responseTime: result.responseTime, id: i }))
                    .catch(error => ({ success: false, error: error.message, id: i }))
            );
        }
        
        const results = await Promise.all(promises);
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        return {
            concurrentRequests,
            successful,
            failed,
            successRate: (successful / concurrentRequests) * 100,
            results
        };
    }

    async testLargeFileUpload() {
        this.log('Testing large file upload stability...', 'network');
        
        // Find test video file
        const testVideoPath = '../test_webm_video.webm';
        if (!fs.existsSync(testVideoPath)) {
            throw new Error('Test video file not found');
        }
        
        const uploadResults = [];
        const uploadAttempts = 5;
        
        for (let i = 0; i < uploadAttempts; i++) {
            try {
                const formData = new FormData();
                const videoBuffer = fs.readFileSync(testVideoPath);
                const blob = new Blob([videoBuffer], { type: 'video/webm' });
                formData.append('file', blob, `stability_test_${i}.webm`);
                
                const { response, responseTime } = await this.makeRequest(
                    `${API_BASE_URL}/predict`,
                    { method: 'POST', body: formData },
                    15000 // 15 second timeout for uploads
                );
                
                const result = await response.json();
                uploadResults.push({
                    success: true,
                    responseTime,
                    fileSize: videoBuffer.length,
                    prediction: result.prediction,
                    attempt: i + 1
                });
                
                this.log(`Upload ${i + 1}/${uploadAttempts} successful (${responseTime}ms)`, 'success');
                
            } catch (error) {
                uploadResults.push({
                    success: false,
                    error: error.message,
                    attempt: i + 1
                });
                
                this.log(`Upload ${i + 1}/${uploadAttempts} failed: ${error.message}`, 'error');
            }
            
            // Wait between uploads
            if (i < uploadAttempts - 1) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        const successfulUploads = uploadResults.filter(r => r.success).length;
        
        return {
            uploadAttempts,
            successfulUploads,
            failedUploads: uploadAttempts - successfulUploads,
            uploadSuccessRate: (successfulUploads / uploadAttempts) * 100,
            results: uploadResults
        };
    }

    async testNetworkRecovery() {
        this.log('Testing network recovery capabilities...', 'network');
        
        // Test rapid successive requests to simulate network stress
        const rapidRequests = 20;
        const results = [];
        
        for (let i = 0; i < rapidRequests; i++) {
            try {
                const { responseTime } = await this.makeRequest(`${API_BASE_URL}/health`, {}, 3000);
                results.push({ success: true, responseTime, attempt: i + 1 });
                
            } catch (error) {
                results.push({ success: false, error: error.message, attempt: i + 1 });
            }
            
            // Very short delay between requests
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        const successfulRequests = results.filter(r => r.success).length;
        
        return {
            rapidRequests,
            successfulRequests,
            failedRequests: rapidRequests - successfulRequests,
            recoveryRate: (successfulRequests / rapidRequests) * 100,
            results
        };
    }

    calculateNetworkMetrics() {
        const metrics = this.testResults.networkMetrics;
        
        if (metrics.responseTimes.length > 0) {
            metrics.averageResponseTime = 
                metrics.responseTimes.reduce((a, b) => a + b, 0) / metrics.responseTimes.length;
        }
        
        return {
            totalRequests: metrics.totalRequests,
            successfulRequests: metrics.successfulRequests,
            failedRequests: metrics.failedRequests,
            timeouts: metrics.timeouts,
            overallSuccessRate: (metrics.successfulRequests / metrics.totalRequests) * 100,
            averageResponseTime: metrics.averageResponseTime,
            maxResponseTime: metrics.maxResponseTime,
            minResponseTime: metrics.minResponseTime === Infinity ? 0 : metrics.minResponseTime
        };
    }

    async runAllNetworkTests() {
        this.log('🚀 Starting Network Stability Testing', 'info');
        this.log('=' * 60, 'info');
        
        try {
            // Basic connectivity
            const basicTest = await this.testBasicConnectivity();
            this.testResults.tests.push({ name: 'Basic Connectivity', result: basicTest });
            
            // Extended connectivity
            const extendedTest = await this.testExtendedConnectivity();
            this.testResults.tests.push({ name: 'Extended Connectivity', result: extendedTest });
            
            // Concurrent connections
            const concurrentTest = await this.testConcurrentConnections();
            this.testResults.tests.push({ name: 'Concurrent Connections', result: concurrentTest });
            
            // Large file uploads
            const uploadTest = await this.testLargeFileUpload();
            this.testResults.tests.push({ name: 'Large File Upload', result: uploadTest });
            
            // Network recovery
            const recoveryTest = await this.testNetworkRecovery();
            this.testResults.tests.push({ name: 'Network Recovery', result: recoveryTest });
            
            this.generateNetworkReport();
            
        } catch (error) {
            this.log(`Critical network test failure: ${error.message}`, 'error');
            this.generateNetworkReport();
            throw error;
        }
    }

    generateNetworkReport() {
        const metrics = this.calculateNetworkMetrics();
        const testDuration = Date.now() - this.testResults.startTime.getTime();
        
        this.log('', 'info');
        this.log('📊 NETWORK STABILITY RESULTS', 'info');
        this.log('=' * 60, 'info');
        this.log(`Test Duration: ${(testDuration / 1000).toFixed(1)}s`, 'info');
        this.log(`Total Requests: ${metrics.totalRequests}`, 'info');
        this.log(`Successful: ${metrics.successfulRequests}`, 'success');
        this.log(`Failed: ${metrics.failedRequests}`, metrics.failedRequests === 0 ? 'info' : 'error');
        this.log(`Timeouts: ${metrics.timeouts}`, metrics.timeouts === 0 ? 'info' : 'warning');
        this.log(`Overall Success Rate: ${metrics.overallSuccessRate.toFixed(1)}%`, 
                 metrics.overallSuccessRate >= 95 ? 'success' : 'warning');
        this.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(0)}ms`, 
                 metrics.averageResponseTime < 1000 ? 'success' : 'warning');
        this.log(`Max Response Time: ${metrics.maxResponseTime}ms`, 'info');
        
        // Save detailed report
        const reportData = {
            ...this.testResults,
            networkMetrics: metrics,
            testDuration
        };
        
        const reportPath = 'network-stability-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
        this.log(`Detailed report saved to: ${reportPath}`, 'info');
        
        // Network stability assessment
        const isStable = metrics.overallSuccessRate >= 95 && 
                        metrics.averageResponseTime < 2000 && 
                        metrics.timeouts < 5;
        
        this.log('', 'info');
        this.log('🎯 NETWORK STABILITY ASSESSMENT', 'info');
        this.log('=' * 60, 'info');
        
        if (isStable) {
            this.log('✅ NETWORK CONNECTION IS STABLE', 'success');
            this.log('Mobile-to-laptop connection is reliable for extended use.', 'success');
        } else {
            this.log('⚠️  NETWORK STABILITY ISSUES DETECTED', 'warning');
            this.log('Connection may be unreliable during extended use.', 'warning');
        }
        
        return isStable;
    }
}

// Run the tests
if (require.main === module) {
    const tester = new NetworkStabilityTester();
    tester.runAllNetworkTests().catch(error => {
        console.error('Network stability test failed:', error);
        process.exit(1);
    });
}

module.exports = NetworkStabilityTester;
