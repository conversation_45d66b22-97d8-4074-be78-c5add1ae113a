{"startTime": "2025-08-26T16:18:07.181Z", "tests": [{"name": "Basic Connectivity", "result": {"status": "healthy", "responseTime": 15, "serverReady": true}}, {"name": "Extended Connectivity", "result": {"totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "maxConsecutiveFailures": 0, "results": [{"success": true, "responseTime": 1, "attempt": 1}, {"success": true, "responseTime": 4, "attempt": 2}, {"success": true, "responseTime": 2, "attempt": 3}, {"success": true, "responseTime": 3, "attempt": 4}, {"success": true, "responseTime": 5, "attempt": 5}, {"success": true, "responseTime": 3, "attempt": 6}, {"success": true, "responseTime": 3, "attempt": 7}, {"success": true, "responseTime": 5, "attempt": 8}, {"success": true, "responseTime": 3, "attempt": 9}, {"success": true, "responseTime": 2, "attempt": 10}, {"success": true, "responseTime": 1, "attempt": 11}, {"success": true, "responseTime": 3, "attempt": 12}, {"success": true, "responseTime": 4, "attempt": 13}, {"success": true, "responseTime": 2, "attempt": 14}, {"success": true, "responseTime": 3, "attempt": 15}, {"success": true, "responseTime": 2, "attempt": 16}, {"success": true, "responseTime": 2, "attempt": 17}, {"success": true, "responseTime": 2, "attempt": 18}, {"success": true, "responseTime": 1, "attempt": 19}, {"success": true, "responseTime": 3, "attempt": 20}, {"success": true, "responseTime": 4, "attempt": 21}, {"success": true, "responseTime": 2, "attempt": 22}, {"success": true, "responseTime": 2, "attempt": 23}, {"success": true, "responseTime": 2, "attempt": 24}, {"success": true, "responseTime": 4, "attempt": 25}, {"success": true, "responseTime": 2, "attempt": 26}, {"success": true, "responseTime": 3, "attempt": 27}, {"success": true, "responseTime": 3, "attempt": 28}, {"success": true, "responseTime": 2, "attempt": 29}, {"success": true, "responseTime": 2, "attempt": 30}, {"success": true, "responseTime": 12, "attempt": 31}, {"success": true, "responseTime": 2, "attempt": 32}, {"success": true, "responseTime": 2, "attempt": 33}, {"success": true, "responseTime": 2, "attempt": 34}, {"success": true, "responseTime": 4, "attempt": 35}, {"success": true, "responseTime": 3, "attempt": 36}, {"success": true, "responseTime": 2, "attempt": 37}, {"success": true, "responseTime": 2, "attempt": 38}, {"success": true, "responseTime": 4, "attempt": 39}, {"success": true, "responseTime": 2, "attempt": 40}, {"success": true, "responseTime": 1, "attempt": 41}, {"success": true, "responseTime": 2, "attempt": 42}, {"success": true, "responseTime": 3, "attempt": 43}, {"success": true, "responseTime": 1, "attempt": 44}, {"success": true, "responseTime": 2, "attempt": 45}, {"success": true, "responseTime": 2, "attempt": 46}, {"success": true, "responseTime": 2, "attempt": 47}, {"success": true, "responseTime": 3, "attempt": 48}, {"success": true, "responseTime": 5, "attempt": 49}, {"success": true, "responseTime": 3, "attempt": 50}]}}, {"name": "Concurrent Connections", "result": {"concurrentRequests": 10, "successful": 10, "failed": 0, "successRate": 100, "results": [{"success": true, "responseTime": 6, "id": 0}, {"success": true, "responseTime": 7, "id": 1}, {"success": true, "responseTime": 7, "id": 2}, {"success": true, "responseTime": 7, "id": 3}, {"success": true, "responseTime": 11, "id": 4}, {"success": true, "responseTime": 10, "id": 5}, {"success": true, "responseTime": 10, "id": 6}, {"success": true, "responseTime": 10, "id": 7}, {"success": true, "responseTime": 10, "id": 8}, {"success": true, "responseTime": 10, "id": 9}]}}, {"name": "Large File Upload", "result": {"uploadAttempts": 5, "successfulUploads": 5, "failedUploads": 0, "uploadSuccessRate": 100, "results": [{"success": true, "responseTime": 5, "fileSize": 262, "prediction": "i want to see my husband", "attempt": 1}, {"success": true, "responseTime": 7, "fileSize": 262, "prediction": "call the nurse", "attempt": 2}, {"success": true, "responseTime": 5, "fileSize": 262, "prediction": "i need help", "attempt": 3}, {"success": true, "responseTime": 5, "fileSize": 262, "prediction": "call the nurse", "attempt": 4}, {"success": true, "responseTime": 6, "fileSize": 262, "prediction": "stay with me please", "attempt": 5}]}}, {"name": "Network Recovery", "result": {"rapidRequests": 20, "successfulRequests": 20, "failedRequests": 0, "recoveryRate": 100, "results": [{"success": true, "responseTime": 1, "attempt": 1}, {"success": true, "responseTime": 2, "attempt": 2}, {"success": true, "responseTime": 3, "attempt": 3}, {"success": true, "responseTime": 2, "attempt": 4}, {"success": true, "responseTime": 2, "attempt": 5}, {"success": true, "responseTime": 3, "attempt": 6}, {"success": true, "responseTime": 2, "attempt": 7}, {"success": true, "responseTime": 2, "attempt": 8}, {"success": true, "responseTime": 2, "attempt": 9}, {"success": true, "responseTime": 2, "attempt": 10}, {"success": true, "responseTime": 2, "attempt": 11}, {"success": true, "responseTime": 5, "attempt": 12}, {"success": true, "responseTime": 2, "attempt": 13}, {"success": true, "responseTime": 2, "attempt": 14}, {"success": true, "responseTime": 2, "attempt": 15}, {"success": true, "responseTime": 0, "attempt": 16}, {"success": true, "responseTime": 2, "attempt": 17}, {"success": true, "responseTime": 3, "attempt": 18}, {"success": true, "responseTime": 2, "attempt": 19}, {"success": true, "responseTime": 2, "attempt": 20}]}}], "networkMetrics": {"totalRequests": 86, "successfulRequests": 86, "failedRequests": 0, "timeouts": 0, "overallSuccessRate": 100, "averageResponseTime": 3.63953488372093, "maxResponseTime": 15, "minResponseTime": 0}, "testDuration": 39745}