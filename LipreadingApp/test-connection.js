// Simple test script to verify network connectivity to the backend server
// Run with: node test-connection.js

const API_BASE_URL = 'http://*************:8000';

async function testConnection() {
  console.log('Testing connection to backend server...');
  console.log(`Server URL: ${API_BASE_URL}`);
  
  try {
    // Test basic health endpoint
    console.log('\n1. Testing basic health endpoint...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    
    if (!healthResponse.ok) {
      throw new Error(`Health check failed: ${healthResponse.status}`);
    }
    
    const healthData = await healthResponse.json();
    console.log('✅ Health check passed:', JSON.stringify(healthData, null, 2));
    
    // Test CTC health endpoint (using main health for simple backend)
    console.log('\n2. Testing CTC health endpoint...');
    const ctcHealthResponse = await fetch(`${API_BASE_URL}/health`);

    if (!ctcHealthResponse.ok) {
      throw new Error(`CTC health check failed: ${ctcHealthResponse.status}`);
    }

    const ctcHealthData = await ctcHealthResponse.json();
    console.log('✅ CTC health check passed:', JSON.stringify(ctcHealthData, null, 2));
    
    // Test status endpoint (skip for simple backend)
    console.log('\n3. Testing status endpoint...');
    console.log('⚠️  Status endpoint not available in simple backend - skipping');
    
    // Summary
    console.log('\n📊 Connection Test Summary:');
    console.log(`- Server Status: ${healthData.status}`);
    console.log(`- VSR Implementation: ${healthData.vsr_impl}`);
    console.log(`- Model Loaded: ${healthData.model_loaded}`);

    if (healthData.model_loaded) {
      console.log('\n✅ Ready for lipreading predictions!');
    } else {
      console.log('\n⚠️  Model not loaded - using demo mode');
    }
    
    return true;
    
  } catch (error) {
    console.error('\n❌ Connection test failed:', error.message);
    
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure the backend server is running');
    console.log('2. Verify your laptop\'s IP address with: ifconfig | grep "inet "');
    console.log('3. Update the API_BASE_URL in config.ts if needed');
    console.log('4. Ensure both devices are on the same Wi-Fi network');
    console.log('5. Check firewall settings on your laptop');
    
    return false;
  }
}

// Run the test
testConnection().then(success => {
  process.exit(success ? 0 : 1);
});
