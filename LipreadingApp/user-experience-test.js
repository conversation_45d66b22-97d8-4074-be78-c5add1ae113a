#!/usr/bin/env node
/**
 * User Experience Testing for ICU Lipreading App
 * Evaluates responsiveness, error handling, and overall usability
 */

const API_BASE_URL = 'http://*************:8000';
const fs = require('fs');

class UserExperienceTester {
    constructor() {
        this.testResults = {
            startTime: new Date(),
            uxTests: [],
            performanceMetrics: {
                responseTimeThresholds: {
                    excellent: 500,    // < 500ms
                    good: 1000,        // < 1s
                    acceptable: 2000,  // < 2s
                    poor: 5000         // < 5s
                },
                responseTimes: [],
                userFlowTimes: []
            },
            errorHandling: {
                gracefulErrors: 0,
                harshErrors: 0,
                recoveryTests: []
            }
        };
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            'info': '👤',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'ux': '🎨',
            'performance': '⚡'
        }[type] || '👤';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async runUXTest(testName, testFunction) {
        this.log(`Testing UX: ${testName}`, 'ux');
        const startTime = Date.now();
        
        try {
            const result = await testFunction();
            const duration = Date.now() - startTime;
            
            this.testResults.uxTests.push({
                name: testName,
                status: 'passed',
                duration,
                result,
                timestamp: new Date()
            });
            
            this.log(`✓ ${testName} completed (${duration}ms)`, 'success');
            return result;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            
            this.testResults.uxTests.push({
                name: testName,
                status: 'failed',
                duration,
                error: error.message,
                timestamp: new Date()
            });
            
            this.log(`✗ ${testName} failed: ${error.message}`, 'error');
            throw error;
        }
    }

    async testAppResponsiveness() {
        // Test various API endpoints for responsiveness
        const endpoints = [
            { name: 'Health Check', url: '/health', threshold: 500 },
            { name: 'Connection Test', url: '/test-connection', threshold: 500 },
            { name: 'Performance Stats', url: '/stats', threshold: 1000 },
            { name: 'Supported Phrases', url: '/phrases', threshold: 500 }
        ];
        
        const results = [];
        
        for (const endpoint of endpoints) {
            const startTime = Date.now();
            
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint.url}`);
                const responseTime = Date.now() - startTime;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const performanceRating = this.rateResponseTime(responseTime, endpoint.threshold);
                this.testResults.performanceMetrics.responseTimes.push(responseTime);
                
                results.push({
                    endpoint: endpoint.name,
                    responseTime,
                    threshold: endpoint.threshold,
                    rating: performanceRating,
                    status: 'success'
                });
                
                this.log(`${endpoint.name}: ${responseTime}ms (${performanceRating})`, 
                         performanceRating === 'poor' ? 'warning' : 'performance');
                
            } catch (error) {
                results.push({
                    endpoint: endpoint.name,
                    error: error.message,
                    status: 'failed'
                });
            }
        }
        
        return { endpoints_tested: endpoints.length, results };
    }

    async testErrorHandling() {
        this.log('Testing error handling scenarios...', 'ux');
        
        const errorTests = [
            {
                name: 'Invalid File Upload',
                test: async () => {
                    const formData = new FormData();
                    const textBlob = new Blob(['invalid content'], { type: 'text/plain' });
                    formData.append('file', textBlob, 'invalid.txt');
                    
                    const response = await fetch(`${API_BASE_URL}/predict`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    return {
                        status: response.status,
                        graceful: response.status === 400, // Should return 400 Bad Request
                        response: await response.json()
                    };
                }
            },
            {
                name: 'Empty File Upload',
                test: async () => {
                    const formData = new FormData();
                    const emptyBlob = new Blob([], { type: 'video/webm' });
                    formData.append('file', emptyBlob, 'empty.webm');
                    
                    const response = await fetch(`${API_BASE_URL}/predict`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    return {
                        status: response.status,
                        graceful: response.status >= 400 && response.status < 500,
                        response: await response.text()
                    };
                }
            },
            {
                name: 'Missing File Parameter',
                test: async () => {
                    const formData = new FormData();
                    // Don't add any file
                    
                    const response = await fetch(`${API_BASE_URL}/predict`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    return {
                        status: response.status,
                        graceful: response.status === 422, // FastAPI validation error
                        response: await response.text()
                    };
                }
            },
            {
                name: 'Non-existent Endpoint',
                test: async () => {
                    const response = await fetch(`${API_BASE_URL}/nonexistent`);
                    
                    return {
                        status: response.status,
                        graceful: response.status === 404,
                        response: await response.text()
                    };
                }
            }
        ];
        
        const results = [];
        
        for (const errorTest of errorTests) {
            try {
                const result = await errorTest.test();
                
                if (result.graceful) {
                    this.testResults.errorHandling.gracefulErrors++;
                    this.log(`✓ ${errorTest.name}: Graceful error handling`, 'success');
                } else {
                    this.testResults.errorHandling.harshErrors++;
                    this.log(`⚠ ${errorTest.name}: Harsh error (status: ${result.status})`, 'warning');
                }
                
                results.push({
                    name: errorTest.name,
                    ...result,
                    graceful_handling: result.graceful
                });
                
            } catch (error) {
                this.testResults.errorHandling.harshErrors++;
                results.push({
                    name: errorTest.name,
                    error: error.message,
                    graceful_handling: false
                });
            }
        }
        
        return { error_tests: errorTests.length, results };
    }

    async testUserFlowSimulation() {
        this.log('Simulating complete user workflow...', 'ux');
        
        const userFlow = [
            {
                step: 'App Launch',
                action: async () => {
                    const response = await fetch(`${API_BASE_URL}/health`);
                    return { connected: response.ok };
                }
            },
            {
                step: 'Check Server Status',
                action: async () => {
                    const response = await fetch(`${API_BASE_URL}/stats`);
                    const stats = await response.json();
                    return { server_ready: stats.model_status.loaded };
                }
            },
            {
                step: 'Get Supported Phrases',
                action: async () => {
                    const response = await fetch(`${API_BASE_URL}/phrases`);
                    const data = await response.json();
                    return { phrases_available: data.phrases.length };
                }
            },
            {
                step: 'Record Video (Simulated)',
                action: async () => {
                    // Simulate 8-second recording delay
                    await new Promise(resolve => setTimeout(resolve, 100)); // Shortened for testing
                    return { recording_completed: true };
                }
            },
            {
                step: 'Upload and Process Video',
                action: async () => {
                    const testVideoPath = '../test_webm_video.webm';
                    if (!fs.existsSync(testVideoPath)) {
                        throw new Error('Test video not available');
                    }
                    
                    const formData = new FormData();
                    const videoBuffer = fs.readFileSync(testVideoPath);
                    const blob = new Blob([videoBuffer], { type: 'video/webm' });
                    formData.append('file', blob, 'user_flow_test.webm');
                    
                    const response = await fetch(`${API_BASE_URL}/predict`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    return {
                        prediction: result.prediction,
                        confidence: result.confidence,
                        processing_time: result.processing_time
                    };
                }
            },
            {
                step: 'Audio Feedback (Simulated)',
                action: async () => {
                    // Simulate TTS processing
                    await new Promise(resolve => setTimeout(resolve, 50)); // Shortened for testing
                    return { audio_ready: true };
                }
            }
        ];
        
        const flowResults = [];
        const flowStartTime = Date.now();
        
        for (const step of userFlow) {
            const stepStartTime = Date.now();
            
            try {
                const result = await step.action();
                const stepDuration = Date.now() - stepStartTime;
                
                flowResults.push({
                    step: step.step,
                    duration: stepDuration,
                    result,
                    status: 'success'
                });
                
                this.log(`${step.step}: ${stepDuration}ms`, 'performance');
                
            } catch (error) {
                const stepDuration = Date.now() - stepStartTime;
                
                flowResults.push({
                    step: step.step,
                    duration: stepDuration,
                    error: error.message,
                    status: 'failed'
                });
                
                this.log(`${step.step} failed: ${error.message}`, 'error');
            }
        }
        
        const totalFlowTime = Date.now() - flowStartTime;
        this.testResults.performanceMetrics.userFlowTimes.push(totalFlowTime);
        
        return {
            total_flow_time: totalFlowTime,
            steps_completed: flowResults.filter(r => r.status === 'success').length,
            steps_failed: flowResults.filter(r => r.status === 'failed').length,
            flow_details: flowResults
        };
    }

    async testAccessibilityFeatures() {
        this.log('Testing accessibility and usability features...', 'ux');
        
        const accessibilityTests = [
            {
                feature: 'Audio Feedback Support',
                test: async () => {
                    // Test that predictions can be converted to speech
                    const testPhrases = ['i need help', 'call the nurse', 'where am i'];
                    return {
                        phrases_tested: testPhrases.length,
                        tts_compatible: testPhrases.every(phrase => phrase.length > 0)
                    };
                }
            },
            {
                feature: 'Confidence Thresholds',
                test: async () => {
                    // Test confidence evaluation logic
                    const ICU_THRESHOLD = 0.65;
                    const OPEN_THRESHOLD = 0.55;
                    
                    const testConfidences = [0.8, 0.6, 0.4, 0.2];
                    const evaluations = testConfidences.map(conf => ({
                        confidence: conf,
                        icu_acceptable: conf >= ICU_THRESHOLD,
                        open_acceptable: conf >= OPEN_THRESHOLD
                    }));
                    
                    return { threshold_logic_working: true, evaluations };
                }
            },
            {
                feature: 'Error Message Clarity',
                test: async () => {
                    // Test that error messages are user-friendly
                    const formData = new FormData();
                    const textBlob = new Blob(['test'], { type: 'text/plain' });
                    formData.append('file', textBlob, 'test.txt');
                    
                    const response = await fetch(`${API_BASE_URL}/predict`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const errorData = await response.json();
                    const errorMessage = errorData.detail || '';
                    
                    return {
                        error_message: errorMessage,
                        user_friendly: errorMessage.includes('video') && !errorMessage.includes('500'),
                        helpful: errorMessage.length > 10
                    };
                }
            }
        ];
        
        const results = [];
        
        for (const test of accessibilityTests) {
            try {
                const result = await test.test();
                results.push({
                    feature: test.feature,
                    ...result,
                    status: 'tested'
                });
                
                this.log(`✓ ${test.feature}: Accessible`, 'success');
                
            } catch (error) {
                results.push({
                    feature: test.feature,
                    error: error.message,
                    status: 'failed'
                });
            }
        }
        
        return { accessibility_tests: accessibilityTests.length, results };
    }

    rateResponseTime(responseTime, threshold = 1000) {
        const thresholds = this.testResults.performanceMetrics.responseTimeThresholds;
        
        if (responseTime < thresholds.excellent) return 'excellent';
        if (responseTime < thresholds.good) return 'good';
        if (responseTime < thresholds.acceptable) return 'acceptable';
        if (responseTime < thresholds.poor) return 'poor';
        return 'unacceptable';
    }

    async runAllUXTests() {
        this.log('🚀 Starting User Experience Testing', 'info');
        this.log('=' * 60, 'info');
        
        try {
            await this.runUXTest('App Responsiveness', () => this.testAppResponsiveness());
            await this.runUXTest('Error Handling', () => this.testErrorHandling());
            await this.runUXTest('User Flow Simulation', () => this.testUserFlowSimulation());
            await this.runUXTest('Accessibility Features', () => this.testAccessibilityFeatures());
            
            this.generateUXReport();
            
        } catch (error) {
            this.log(`Critical UX test failure: ${error.message}`, 'error');
            this.generateUXReport();
            throw error;
        }
    }

    generateUXReport() {
        const testDuration = Date.now() - this.testResults.startTime.getTime();
        const passedTests = this.testResults.uxTests.filter(t => t.status === 'passed').length;
        const failedTests = this.testResults.uxTests.filter(t => t.status === 'failed').length;
        
        // Calculate performance metrics
        const avgResponseTime = this.testResults.performanceMetrics.responseTimes.length > 0 ?
            this.testResults.performanceMetrics.responseTimes.reduce((a, b) => a + b, 0) / 
            this.testResults.performanceMetrics.responseTimes.length : 0;
        
        const errorHandlingScore = this.testResults.errorHandling.gracefulErrors / 
            Math.max(this.testResults.errorHandling.gracefulErrors + this.testResults.errorHandling.harshErrors, 1) * 100;
        
        this.log('', 'info');
        this.log('📊 USER EXPERIENCE TEST RESULTS', 'info');
        this.log('=' * 60, 'info');
        this.log(`Test Duration: ${(testDuration / 1000).toFixed(1)}s`, 'info');
        this.log(`UX Tests Passed: ${passedTests}`, 'success');
        this.log(`UX Tests Failed: ${failedTests}`, failedTests === 0 ? 'info' : 'error');
        this.log(`Average Response Time: ${avgResponseTime.toFixed(0)}ms`, 
                 avgResponseTime < 1000 ? 'success' : 'warning');
        this.log(`Error Handling Score: ${errorHandlingScore.toFixed(1)}%`, 
                 errorHandlingScore >= 75 ? 'success' : 'warning');
        
        // Save detailed report
        const reportData = {
            ...this.testResults,
            summary: {
                testDuration,
                passedTests,
                failedTests,
                avgResponseTime,
                errorHandlingScore
            }
        };
        
        const reportPath = 'user-experience-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
        this.log(`Detailed report saved to: ${reportPath}`, 'info');
        
        // UX assessment
        const uxScore = (passedTests / Math.max(passedTests + failedTests, 1)) * 100;
        const isUsable = uxScore >= 80 && avgResponseTime < 2000 && errorHandlingScore >= 75;
        
        this.log('', 'info');
        this.log('🎯 USER EXPERIENCE ASSESSMENT', 'info');
        this.log('=' * 60, 'info');
        
        if (isUsable) {
            this.log('✅ EXCELLENT USER EXPERIENCE', 'success');
            this.log('App is responsive, handles errors gracefully, and provides good usability.', 'success');
        } else {
            this.log('⚠️  USER EXPERIENCE NEEDS IMPROVEMENT', 'warning');
            this.log('Some usability issues detected that may impact user satisfaction.', 'warning');
        }
        
        return isUsable;
    }
}

// Run the tests
if (require.main === module) {
    const tester = new UserExperienceTester();
    tester.runAllUXTests().catch(error => {
        console.error('User experience test failed:', error);
        process.exit(1);
    });
}

module.exports = UserExperienceTester;
