{"startTime": "2025-08-26T16:20:11.775Z", "uxTests": [{"name": "App Responsiveness", "status": "passed", "duration": 21, "result": {"endpoints_tested": 4, "results": [{"endpoint": "Health Check", "responseTime": 17, "threshold": 500, "rating": "excellent", "status": "success"}, {"endpoint": "Connection Test", "responseTime": 2, "threshold": 500, "rating": "excellent", "status": "success"}, {"endpoint": "Performance Stats", "responseTime": 0, "threshold": 1000, "rating": "excellent", "status": "success"}, {"endpoint": "Supported Phrases", "responseTime": 0, "threshold": 500, "rating": "excellent", "status": "success"}]}, "timestamp": "2025-08-26T16:20:11.801Z"}, {"name": "Erro<PERSON>", "status": "passed", "duration": 10, "result": {"error_tests": 4, "results": [{"name": "Invalid File Upload", "status": 400, "graceful": true, "response": {"detail": "File must be a video"}, "graceful_handling": true}, {"name": "Empty File Upload", "status": 200, "graceful": false, "response": "{\"success\":true,\"prediction\":\"what happened to me\",\"confidence\":0.6066786256630335,\"model_type\":\"dummy_demo\",\"processing_time\":0.0009951591491699219,\"file_size\":0}", "graceful_handling": false}, {"name": "Missing File Parameter", "status": 422, "graceful": true, "response": "{\"detail\":[{\"type\":\"missing\",\"loc\":[\"body\",\"file\"],\"msg\":\"Field required\",\"input\":null}]}", "graceful_handling": true}, {"name": "Non-existent Endpoint", "status": 404, "graceful": true, "response": "{\"detail\":\"Not Found\"}", "graceful_handling": true}]}, "timestamp": "2025-08-26T16:20:11.811Z"}, {"name": "User Flow Simulation", "status": "passed", "duration": 159, "result": {"total_flow_time": 159, "steps_completed": 6, "steps_failed": 0, "flow_details": [{"step": "App Launch", "duration": 2, "result": {"connected": true}, "status": "success"}, {"step": "Check Server Status", "duration": 0, "result": {"server_ready": true}, "status": "success"}, {"step": "Get Supported Phrases", "duration": 1, "result": {"phrases_available": 10}, "status": "success"}, {"step": "Record Video (Simulated)", "duration": 101, "result": {"recording_completed": true}, "status": "success"}, {"step": "Upload and Process Video", "duration": 4, "result": {"prediction": "stay with me please", "confidence": 0.8178864417351913, "processing_time": 0.0005571842193603516}, "status": "success"}, {"step": "Audio Feedback (Simulated)", "duration": 51, "result": {"audio_ready": true}, "status": "success"}]}, "timestamp": "2025-08-26T16:20:11.970Z"}, {"name": "Accessibility Features", "status": "passed", "duration": 2, "result": {"accessibility_tests": 3, "results": [{"feature": "Audio Feedback Support", "phrases_tested": 3, "tts_compatible": true, "status": "tested"}, {"feature": "Confidence Thresholds", "threshold_logic_working": true, "evaluations": [{"confidence": 0.8, "icu_acceptable": true, "open_acceptable": true}, {"confidence": 0.6, "icu_acceptable": false, "open_acceptable": true}, {"confidence": 0.4, "icu_acceptable": false, "open_acceptable": false}, {"confidence": 0.2, "icu_acceptable": false, "open_acceptable": false}], "status": "tested"}, {"feature": "Error Message Clarity", "error_message": "File must be a video", "user_friendly": true, "helpful": true, "status": "tested"}]}, "timestamp": "2025-08-26T16:20:11.972Z"}], "performanceMetrics": {"responseTimeThresholds": {"excellent": 500, "good": 1000, "acceptable": 2000, "poor": 5000}, "responseTimes": [17, 2, 0, 0], "userFlowTimes": [159]}, "errorHandling": {"gracefulErrors": 3, "harshErrors": 1, "recoveryTests": []}, "summary": {"testDuration": 197, "passedTests": 4, "failedTests": 0, "avgResponseTime": 4.75, "errorHandlingScore": 75}}