{"startTime": "2025-08-26T16:15:36.339Z", "tests": [{"name": "Basic Connectivity", "status": "passed", "duration": 18, "result": {"status": "healthy", "model_loaded": true, "vsr_impl": "lightweight"}, "timestamp": "2025-08-26T16:15:36.362Z"}, {"name": "Performance Stats", "status": "passed", "duration": 2, "result": {"performance": {"total_requests": 0, "successful_predictions": 0, "failed_predictions": 0, "average_processing_time": 0, "last_request_time": null, "uptime_start": "2025-08-27T00:15:23.036133"}, "uptime_seconds": 13.32811, "uptime_formatted": "0:00:13.328110", "requests_per_minute": 0, "success_rate": 0, "model_status": {"loaded": true, "type": "dummy_demo"}}, "timestamp": "2025-08-26T16:15:36.364Z"}, {"name": "Video Upload & Prediction", "status": "passed", "duration": 5, "result": {"prediction": "stay with me please", "confidence": 0.6929921051421469, "upload_time": 5, "processing_time": 0.000759124755859375, "file_size": 262}, "timestamp": "2025-08-26T16:15:36.370Z"}, {"name": "Network Stability", "status": "passed", "duration": 5018, "result": {"iterations": 5, "average_response_time": 2.4, "max_response_time": 6, "all_response_times": [0, 2, 1, 6, 3]}, "timestamp": "2025-08-26T16:15:41.389Z"}, {"name": "Stress Load Testing", "status": "passed", "duration": 15, "result": {"total": 3, "successful": 3, "failed": 0, "success_rate": 100}, "timestamp": "2025-08-26T16:15:41.404Z"}], "summary": {"total": 5, "passed": 5, "failed": 0, "warnings": 1}}