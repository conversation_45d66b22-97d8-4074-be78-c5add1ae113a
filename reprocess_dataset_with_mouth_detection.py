#!/usr/bin/env python3
"""
Reprocess the entire dataset with mouth detection and cropping
This will create a new preprocessed dataset for improved training
"""

import os
import sys
import json
import time
from pathlib import Path
import pandas as pd
from tqdm import tqdm
import logging

# Add backend to path
sys.path.append('backend')

from backend.lightweight_vsr.utils_video import VideoProcessor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def reprocess_dataset():
    """Reprocess the entire dataset with mouth detection"""
    print("🔄 Reprocessing Dataset with Mouth Detection")
    print("=" * 60)
    
    # Configuration
    config = {
        "frames": 32,
        "height": 96,
        "width": 96,
        "grayscale": True,
        "use_mouth_detection": True,
        "mouth_detection_fallback": True
    }
    
    # Create processor with mouth detection
    print("🔧 Initializing video processor with mouth detection...")
    processor = VideoProcessor(
        target_frames=config["frames"],
        target_size=(config["height"], config["width"]),
        grayscale=config["grayscale"],
        use_mouth_detection=config["use_mouth_detection"],
        mouth_detection_fallback=config["mouth_detection_fallback"]
    )
    
    # Find manifest file
    manifest_path = Path("data/manifest.csv")
    if not manifest_path.exists():
        print("❌ Manifest file not found: data/manifest.csv")
        return False
    
    # Load manifest
    print("📋 Loading dataset manifest...")
    df = pd.read_csv(manifest_path)
    print(f"📊 Found {len(df)} videos in manifest")
    
    # Create output directory for preprocessed data
    output_dir = Path("preprocessed_mouth_detected")
    output_dir.mkdir(exist_ok=True)
    
    # Statistics tracking
    stats = {
        "total_videos": len(df),
        "processed_successfully": 0,
        "mouth_detection_failures": 0,
        "processing_errors": 0,
        "phrases": {},
        "start_time": time.time()
    }
    
    # Process each video
    print("\n🎬 Processing videos with mouth detection...")
    
    processed_manifest = []
    
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
        video_path = Path(row['video_path'])
        phrase = row['phrase']
        
        # Track phrase statistics
        if phrase not in stats["phrases"]:
            stats["phrases"][phrase] = {"total": 0, "success": 0, "failures": 0}
        stats["phrases"][phrase]["total"] += 1
        
        try:
            if not video_path.exists():
                logger.warning(f"Video not found: {video_path}")
                stats["processing_errors"] += 1
                stats["phrases"][phrase]["failures"] += 1
                continue
            
            # Process video with mouth detection
            tensor = processor.process_video(video_path)
            
            # Create output filename
            output_filename = f"{video_path.stem}_mouth_detected.pt"
            output_path = output_dir / output_filename
            
            # Save preprocessed tensor
            import torch
            torch.save(tensor, output_path)
            
            # Add to processed manifest
            processed_manifest.append({
                "original_path": str(video_path),
                "preprocessed_path": str(output_path),
                "phrase": phrase,
                "tensor_shape": list(tensor.shape),
                "processing_config": config
            })
            
            stats["processed_successfully"] += 1
            stats["phrases"][phrase]["success"] += 1
            
        except Exception as e:
            logger.error(f"Error processing {video_path}: {e}")
            stats["processing_errors"] += 1
            stats["phrases"][phrase]["failures"] += 1
    
    # Save processed manifest
    processed_manifest_path = output_dir / "processed_manifest.json"
    with open(processed_manifest_path, 'w') as f:
        json.dump(processed_manifest, f, indent=2)
    
    # Calculate final statistics
    stats["end_time"] = time.time()
    stats["processing_time"] = stats["end_time"] - stats["start_time"]
    stats["success_rate"] = stats["processed_successfully"] / stats["total_videos"]
    
    # Save statistics
    stats_path = output_dir / "processing_stats.json"
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=2)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Dataset Reprocessing Summary")
    print("=" * 60)
    
    print(f"Total Videos: {stats['total_videos']}")
    print(f"Successfully Processed: {stats['processed_successfully']}")
    print(f"Processing Errors: {stats['processing_errors']}")
    print(f"Success Rate: {stats['success_rate']:.1%}")
    print(f"Processing Time: {stats['processing_time']:.1f} seconds")
    
    print(f"\n📁 Output Directory: {output_dir}")
    print(f"📋 Processed Manifest: {processed_manifest_path}")
    print(f"📊 Statistics: {stats_path}")
    
    # Phrase-level statistics
    print("\n📝 Per-Phrase Statistics:")
    for phrase, phrase_stats in stats["phrases"].items():
        success_rate = phrase_stats["success"] / phrase_stats["total"] if phrase_stats["total"] > 0 else 0
        print(f"  {phrase}: {success_rate:.1%} ({phrase_stats['success']}/{phrase_stats['total']})")
    
    if stats["success_rate"] > 0.8:
        print("\n🎉 Dataset reprocessing completed successfully!")
        print("✅ Ready for retraining with mouth-detected videos")
        return True
    else:
        print(f"\n⚠️  Low success rate ({stats['success_rate']:.1%}). Review errors above.")
        return False

def verify_preprocessed_data():
    """Verify the preprocessed data quality"""
    print("\n🔍 Verifying Preprocessed Data Quality")
    print("-" * 40)
    
    output_dir = Path("preprocessed_mouth_detected")
    manifest_path = output_dir / "processed_manifest.json"
    
    if not manifest_path.exists():
        print("❌ No processed manifest found")
        return False
    
    with open(manifest_path) as f:
        manifest = json.load(f)
    
    print(f"📊 Verifying {len(manifest)} preprocessed files...")
    
    # Sample verification
    import torch
    sample_count = min(5, len(manifest))
    
    for i in range(sample_count):
        item = manifest[i]
        preprocessed_path = Path(item["preprocessed_path"])
        
        if preprocessed_path.exists():
            tensor = torch.load(preprocessed_path)
            expected_shape = tuple(item["tensor_shape"])
            
            if tensor.shape == expected_shape:
                print(f"  ✅ {preprocessed_path.name}: {tensor.shape}")
            else:
                print(f"  ❌ {preprocessed_path.name}: {tensor.shape} (expected {expected_shape})")
        else:
            print(f"  ❌ {preprocessed_path.name}: File not found")
    
    print("✅ Preprocessed data verification complete")
    return True

if __name__ == "__main__":
    success = reprocess_dataset()
    
    if success:
        verify_preprocessed_data()
        print("\n🚀 Ready to retrain with mouth-detected dataset!")
    else:
        print("\n❌ Dataset reprocessing failed")
    
    sys.exit(0 if success else 1)
