#!/usr/bin/env python3
"""
Demo backend for mobile lipreading integration
"""

from fastapi import FastAPI, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import random

app = FastAPI()

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Demo phrases
DEMO_PHRASES = [
    "where am i",
    "i need help", 
    "i m in pain",
    "i feel anxious",
    "my chest hurts",
    "call the nurse",
    "i want to see my husband",
    "what happened to me",
    "stay with me please",
    "i need water"
]

@app.get("/")
async def root():
    return {"message": "ICU Lipreading Demo API", "status": "running"}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "vsr_impl": "lightweight",
        "model_loaded": True
    }

@app.post("/predict_v2")
async def predict(file: UploadFile = File(...)):
    # Demo prediction
    phrase = random.choice(DEMO_PHRASES)
    confidence = random.uniform(0.6, 0.9)
    
    return {
        "success": True,
        "prediction": phrase,
        "confidence": confidence,
        "model_type": "demo"
    }

@app.get("/phrases")
async def get_phrases():
    return {"phrases": DEMO_PHRASES}

if __name__ == "__main__":
    print("🚀 Starting Demo Backend on port 8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
