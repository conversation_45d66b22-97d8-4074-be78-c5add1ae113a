#!/usr/bin/env python3
"""
Test the integrated mouth detection video processing pipeline
"""

import sys
import os
from pathlib import Path
import cv2
import numpy as np

# Add backend to path
sys.path.append('backend')

from backend.lightweight_vsr.utils_video import VideoProcessor

def test_mouth_detection_integration():
    """Test the complete mouth detection integration"""
    print("🧪 Testing Mouth Detection Integration")
    print("=" * 50)
    
    # Find test videos
    test_video_paths = [
        Path("test_webm_videos"),
        Path("mouth_cropped_videos"),
        Path("data/where_am_i")
    ]
    
    test_videos = []
    for test_dir in test_video_paths:
        if test_dir.exists():
            videos = list(test_dir.glob("*.webm"))[:3]  # Test first 3 videos
            if videos:
                test_videos.extend(videos)
                break
    
    if not test_videos:
        print("❌ No test videos found")
        return False
    
    print(f"📹 Found {len(test_videos)} test videos")
    
    # Test configurations
    configs = [
        {
            "name": "With Mouth Detection",
            "config": {
                "frames": 32,
                "height": 96,
                "width": 96,
                "grayscale": True,
                "use_mouth_detection": True,
                "mouth_detection_fallback": True
            }
        },
        {
            "name": "Without Mouth Detection (Legacy)",
            "config": {
                "frames": 32,
                "height": 96,
                "width": 96,
                "grayscale": True,
                "use_mouth_detection": False,
                "mouth_detection_fallback": False
            }
        }
    ]
    
    results = []
    
    for config_info in configs:
        config_name = config_info["name"]
        config = config_info["config"]
        
        print(f"\n🔧 Testing: {config_name}")
        print("-" * 30)
        
        try:
            # Create processor
            processor = VideoProcessor(
                target_frames=config["frames"],
                target_size=(config["height"], config["width"]),
                grayscale=config["grayscale"],
                use_mouth_detection=config["use_mouth_detection"],
                mouth_detection_fallback=config["mouth_detection_fallback"]
            )
            
            success_count = 0
            total_count = 0
            
            for video_path in test_videos:
                total_count += 1
                try:
                    print(f"  📹 Processing: {video_path.name}")
                    
                    # Process video
                    tensor = processor.process_video(video_path)
                    
                    # Check output shape
                    expected_shape = (1, config["frames"], config["height"], config["width"])
                    if tensor.shape == expected_shape:
                        print(f"    ✅ Shape: {tensor.shape}")
                        success_count += 1
                    else:
                        print(f"    ❌ Wrong shape: {tensor.shape}, expected: {expected_shape}")
                        
                except Exception as e:
                    print(f"    ❌ Error: {e}")
            
            success_rate = success_count / total_count if total_count > 0 else 0
            print(f"\n  📊 Success Rate: {success_rate:.1%} ({success_count}/{total_count})")
            
            results.append({
                "config": config_name,
                "success_rate": success_rate,
                "success_count": success_count,
                "total_count": total_count
            })
            
        except Exception as e:
            print(f"  ❌ Configuration failed: {e}")
            results.append({
                "config": config_name,
                "success_rate": 0.0,
                "success_count": 0,
                "total_count": len(test_videos)
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Integration Test Results")
    print("=" * 50)
    
    for result in results:
        status = "✅ PASS" if result["success_rate"] > 0.5 else "❌ FAIL"
        print(f"{result['config']:.<35} {status} ({result['success_rate']:.1%})")
    
    # Overall assessment
    mouth_detection_result = next((r for r in results if "Mouth Detection" in r["config"]), None)
    legacy_result = next((r for r in results if "Legacy" in r["config"]), None)
    
    if mouth_detection_result and mouth_detection_result["success_rate"] > 0.5:
        print("\n🎉 Mouth detection integration successful!")
        
        if legacy_result and mouth_detection_result["success_rate"] >= legacy_result["success_rate"]:
            print("✅ Mouth detection performs as well as or better than legacy processing")
        
        return True
    else:
        print("\n❌ Mouth detection integration needs improvement")
        return False

if __name__ == "__main__":
    success = test_mouth_detection_integration()
    sys.exit(0 if success else 1)
